<script setup lang="ts">
import type { UserProperties } from '@db/apps/users/types'
import { useHead } from '@unhead/vue'
import { useNotyf } from '@/composables/useNotyf'
import { themeConfig } from '@themeConfig'
import DetailPeserta from '@/views/pages/apps/pendaftaran/peserta/DetailPeserta.vue'
import avatar1 from '@images/avatars/default-avatar.png'
import AddNewPeserta from '@/views/pages/apps/peserta-kwarda/AddNewPeserta.vue'
import UpdatePeserta from '@/views/pages/apps/peserta-kwarda/UpdatePeserta.vue'

// 👉 Store
const baseURL = import.meta.env.VITE_API_BASE_URL
const searchQuery = ref('')
const selectedStatus = ref()
const errors = ref()
const notyf = useNotyf()
const isLoading = ref(false)
const isLoadingExcel = ref(false)
const isConfirmDialogVisible = ref(false)
const isFormDialogVisible = ref(false)
const selected = ref<Kwarcab>()
const selectedData = ref()
const isDetailFormDialogVisible = ref(false)
const isEditFormDialogVisible = ref(false)
const isKwarda = ref(true)
const isMusda = ref(true)

// Data table options
const itemsPerPage = ref(10)
const page = ref(1)
const sortBy = ref()
const orderBy = ref()
const kwarcabId = ref()

interface Kwarcab {
  id?: string
  code?: string
  name: string
}
const kwarcabs = ref<Kwarcab>()

// Update data table options
const updateOptions = (options: any) => {
  sortBy.value = options.sortBy[0]?.key
  orderBy.value = options.sortBy[0]?.order
}

const openDetailForm = (row: any) => {
  isDetailFormDialogVisible.value = true
  selectedData.value = row
}

const headers = [
  { title: '#', key: 'index' },
  { title: 'Foto', key: 'foto', width: 400 },
  { title: 'Kwarcab', key: 'kwarcab', width: 400 },
  { title: 'Nama', key: 'nama', width: 400 },
  { title: 'No.HP', key: 'no_hp', width: 400 },
  { title: 'Jabatan', key: 'jabatan', width: 400 },
  { title: 'Aksi', key: 'actions', sortable: false },
]

// 👉 Fetching users
const { data: pesetaData, execute: fetchData } = await useApi<any>(createUrl('/peserta/list', {
  query: {
    q: searchQuery,
    status: selectedStatus,
    limit: itemsPerPage,
    offset: page,
    sortBy,
    orderBy,
    kwarcab_id: kwarcabId,
    is_status: 'musda',
    is_kwarda: isKwarda.value ? '1' : '0'	,
  },
}))

const users = computed((): UserProperties[] => pesetaData.value.content!.pesertas)
const totalData = computed(() => pesetaData.value.content.total)

const resolveUserStatusVariant = (stat: number) => {
  const statLowerCase = stat
  if (statLowerCase === 1) {
    return {
      variant: 'primary',
      label: 'Ketua/ Ketua Harian',
    }
  }

  if (statLowerCase === 2) {
    return {
      variant: 'success',
      label: 'Sekretaris',
    }
  }
  if (statLowerCase === 3) {
    return {
      variant: 'info',
      label: 'Waka Orgakum/ Waka lainnya',
    }
  }
  if (statLowerCase === 4) {
    return {
      variant: 'warning',
      label: 'Kapusdiklatcab',
    }
  }

  return {
    variant: 'danger',
    label: 'Ketua DKC',
  }
}

const resolveAvatar = (foto: string) => {
  if (foto !== '')
    return `${baseURL}/storage/uploads/photos/peserta/${foto}`

  return avatar1
}

const isAddNewDivisiDrawerVisible = ref(false)

// 👉 Update new Divisi

const getKwarcab = async () => {
  try {
    const res = await $api('/settings/get-kwarcab', {
      method: 'GET',
      onResponseError({ response }) {
        errors.value = response._data.errors
      },
    })

    kwarcabs.value = res.content

    await nextTick(() => {
      // router.replace(route.query.to ? String(route.query.to) : '/')
    })
  }
  catch (err) {
    console.error(err)
  }
}

// 👉 Delete data
const deleteData = async (id: number) => {
  await $api(`/apps/users/${id}`, {
    method: 'DELETE',
  })

  // refetch User
  // TODO: Make this async
  fetchData()
}
const openAddForm = (row: any) => {
  isFormDialogVisible.value = true
}

const openDeleteDataSelected = (row: any) => {
  selected.value = row
  isConfirmDialogVisible.value = true
}

const openEditDataSelected = (row: any) => {
  selectedData.value = row
  isEditFormDialogVisible.value = true
}

watchEffect(() => {
  if (!isAddNewDivisiDrawerVisible.value)
    selectedData.value = null
})
useHead({
  title: `Peserta Musda - ${themeConfig.app.title}`,
})
definePage({
  meta: {
    action: 'List User',
    subject: 'peserta.list',
  },
})

const downloadFile = async () => {
  isLoading.value = true

  try {
    const res: Blob = await $api('/badge/peserta-download', {
      method: 'GET',
      responseType: 'blob',
      params: {
        is_kwarda: isKwarda.value,

      },

      onResponseError({ response }) {
        isLoading.value = false

        errors.value = response._data.errors
      },
    })

    isLoading.value = false

    const fileURL = window.URL.createObjectURL(new Blob([res]))
    const fileLink = document.createElement('a')

    fileLink.href = fileURL

    fileLink.setAttribute(
      'download',
      'ID-CARD-PESERTA-MUSDA-KWARDA.zip',
    )
    document.body.appendChild(fileLink)
    fileLink.click()
  }
  catch (err) {
    isLoading.value = false
    console.error(err)
  }
}

const downloadExcel = async () => {
  isLoadingExcel.value = true

  try {
    const res: Blob = await $api('/peserta/export-excel', {
      method: 'GET',
      responseType: 'blob',
      params: {
        is_musda: isKwarda.value,
        is_status: 'musda',
      },

      onResponseError({ response }) {
        isLoadingExcel.value = false
        notyf.dismissAll()
        notyf.error(response._data.message || 'Gagal mengunduh data')
        errors.value = response._data.errors
      },
    })

    isLoadingExcel.value = false

    const fileURL = window.URL.createObjectURL(new Blob([res]))
    const fileLink = document.createElement('a')

    fileLink.href = fileURL

    fileLink.setAttribute(
      'download',
      'PESERTA-MUSDA-KWARDA.xlsx',
    )
    document.body.appendChild(fileLink)
    fileLink.click()
  }
  catch (err) {
    isLoadingExcel.value = false
    console.error(err)
    notyf.dismissAll()
    notyf.error('Terjadi kesalahan saat mengunduh data')
  }
}

const downloadIdCard = async (row: any) => {

  try {
    const res: Blob = await $api(`/badge/download/idcard/peserta-musda/${row.id}`, {
      method: 'GET',
      responseType: 'blob',

      onResponseError({ response }) {

        errors.value = response._data.errors
      },
    })

    const fileURL = window.URL.createObjectURL(new Blob([res]))
    const fileLink = document.createElement('a')

    fileLink.href = fileURL

    fileLink.setAttribute(
      'download',
      `ID-CARD-PESERTA-MUSDA-KWARDA-${row.nomor_peserta}.png`,
    )
    document.body.appendChild(fileLink)
    fileLink.click()
  }
  catch (err) {
    console.error(err)
  }
}


onMounted(() => {
  getKwarcab()
})
</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardText class="d-flex flex-wrap gap-4">
        <!-- 👉 Download Excel button -->
        <VBtn :loading="isLoadingExcel" color="success" prepend-icon="tabler-file-spreadsheet"
          :disable="users.length === 0" @click.prevent="downloadExcel">
          Export Excel
        </VBtn>
        <VSpacer />

        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <!-- 👉 Search  -->
          <div style="inline-size: 15.625rem;">
            <AppTextField v-model="searchQuery" placeholder="Cari..." />
          </div>

          <!-- 👉 Add user button -->
          <VBtn prepend-icon="tabler-plus" @click="openAddForm">
            Baru
          </VBtn>
        </div>
      </VCardText>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer v-model:items-per-page="itemsPerPage" v-model:page="page" :items="users"
        :items-length="totalData" :headers="headers" class="text-no-wrap" show-select @update:options="updateOptions">
        <!-- index -->
        <template #item.index="{ index }">
          <div class="d-flex align-center">
            <div class="d-flex flex-column">
              {{ Math.ceil(page * itemsPerPage - itemsPerPage + index + 1) }}
            </div>
          </div>
        </template>

        <!-- foto -->
        <template #item.foto="{ item }">
          <div class="text-body-1 text-high-emphasis text-capitalize">
            <div class="me-4">
              <VAvatar :image="resolveAvatar(item.lampiran_foto)" width="50" />
            </div>
          </div>
        </template>

        <!-- bidang -->
        <template #item.divisi="{ item }">
          <div class="text-body-1 text-high-emphasis text-capitalize">
            {{ item.nama_divisi }}
          </div>
        </template>

        <!-- Status -->
        <template #item.jabatan="{ item }">
          <VChip :color="resolveUserStatusVariant(item.jabatan_id).variant" size="small" label class="text-capitalize">
            {{ item.jabatan }}
          </VChip>
        </template>

        <!-- Actions -->
        <template #item.actions="{ item }">
          <IconBtn @click="downloadIdCard(item)">
            <VIcon icon="tabler-id-badge-2" />
          </IconBtn>
          <IconBtn @click="openEditDataSelected(item)">
            <VIcon icon="tabler-pencil" />
          </IconBtn>
          <IconBtn @click="openDeleteDataSelected(item)">
            <VIcon icon="tabler-trash" />
          </IconBtn>

          <IconBtn @click="openDetailForm(item)">
            <VIcon icon="tabler-eye" />
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination v-model:page="page" :items-per-page="itemsPerPage" :total-items="totalData" />
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
    <AddNewPeserta v-model:isFormDialogVisible="isFormDialogVisible" :jabatan-selected="selectedData"
      :is-musda="isMusda" @refetch="fetchData" />
    <UpdatePeserta v-model:isEditFormDialogVisible="isEditFormDialogVisible" :panitia-data="selectedData"
      :is-musda="isMusda" @refetch="fetchData" />
    <DetailPeserta v-model:isDetailFormDialogVisible="isDetailFormDialogVisible" :user-data="selectedData"
      @refetch="fetchData" />
    <!-- 👉 Confirm Dialog -->
    <ConfirmDialog v-model:isDialogVisible="isConfirmDialogVisible" cancel-title="Batal" confirm-title="Berhasil!"
      confirm-msg="Peserta berhasil dihapus!." confirmation-question="Yakin ingin hapus Peserta ini ?"
      cancel-msg="Batal hapus Peserta!" @submit="deleteData" />
  </section>
</template>
