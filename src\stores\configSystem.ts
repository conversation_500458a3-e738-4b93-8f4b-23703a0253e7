import { acceptHMRUpdate, defineStore } from 'pinia'
import { ref } from 'vue'
import { useNotyf } from '@/composables/useNotyf'

interface ConfigSystem {
  app_name: string
  country: string
  email?: string
  phone?: string
  province_code: string
  status_pendaftaran?: boolean
  timeformat: number
  timezone: string

}

// const errors = ref<any> ()
export const useConfigSystem = defineStore('configSystem', () => {
  const configSystem = ref<ConfigSystem>()
  const notyf = useNotyf()
  async function setConfigSystem() {
    try {
      const res = await $api('/settings/get-config', {
        method: 'GET',

        onResponseError({ response }) {
          notyf.dismissAll()
          notyf.error(response._data.content[0].message!)
        },

      })

      configSystem.value = res.content
    }
    catch (err) {
      console.error(err)
    }
  }

  return {
    configSystem,
    setConfigSystem,

  } as const
})

/**
 * Pinia supports Hot Module replacement so you can edit your stores and
 * interact with them directly in your apps without reloading the page.
 *
 * @see https://pinia.esm.dev/cookbook/hot-module-replacement.html
 * @see https://vitejs.dev/guide/api-hmr.html
 */
if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useConfigSystem, import.meta.hot))
