<script setup lang="ts">
import { useNotyf } from '@/composables/useNotyf'
import { usePendaftaran } from '@/stores/pendaftaran'
import { readableFileSize } from '@core/utils/formatters'
import { useConfigSystem } from '@/stores/configSystem'
import UploadMandat from '@/views/pages/apps/pendaftaran/mandat/UploadMandat.vue'

const isPendaftaran = usePendaftaran()
const configSystem = useConfigSystem()

const mandatMusda = ref(null)
const isMandat = ref(null)
const notyf = useNotyf()
const isLoading = ref(false)
const isConfirmDialogVisible = ref(false)
const isOpenDialogVisible = ref(false)
const errors = ref()
const mandats = ref([])

const resolveIcon = (extension: string) => {
  if (extension === 'jpg' || extension === 'jpeg')
    return 'tabler-file-type-jpg'
  if (extension === 'png')
    return 'tabler-file-type-png'
  if (extension === 'pdf')
    return 'tabler-file-type-pdf'

  return 'tabler-file'
}

const openConfirmDialog = (row: any) => {
  isConfirmDialogVisible.value = true
  isMandat.value = row
}

const openUploadDialog = (row: any) => {
  isOpenDialogVisible.value = true
  isMandat.value = row
}

const deleteMandat = async () => {
  try {
    // isLoading.value = true

    await $api(`/pendaftaran/delete/${isMandat.value?.pendaftaran!.id}`, {
      method: 'POST',
      onResponseError({ response }) {
        isLoading.value = false
        notyf.dismissAll()
        notyf.error(response._data.content[0].message!)
      },
    })

    // isLoading.value = false

    // notyf.dismissAll()
    // notyf.success('Lampiran Mandat berhasil dihapus')
    await getMandat()
    await isPendaftaran.getPendaftaran()
  }
  catch (err) {
    isLoading.value = false
    console.error(err)
  }
}

const getMandat = async () => {
  try {
    const res = await $api('/pendaftaran/get', {
      method: 'GET',
      onResponseError({ response }) {
        errors.value = response._data.errors
      },
    })

    mandats.value = res.content

    await nextTick(() => {
      // router.replace(route.query.to ? String(route.query.to) : '/')
    })
  }
  catch (err) {
    console.error(err)
  }
}

getMandat()
</script>

<template>
  <VCard>
    <VCardItem>
      <template #append>
        <span class="text-primary font-weight-medium text-sm cursor-pointer" />
      </template>
    </VCardItem>
    <!-- 👉 Submit and Cancel -->
    <VCol
      cols="12"
      class="justify-center mb-5"
    >
      <VList
        lines="two"
        border
      >
        <template
          v-for="(mandat, index) of mandats"
          :key="index"
        >
          <VListItem>
            <template #prepend>
              <VAvatar
                color="primary"
                variant="tonal"
                :icon="resolveIcon(mandat?.pendaftaran?.extension)"
              />
            </template>
            <VListItemTitle>
              Lampiran Mandat {{ mandat?.mandat }}
            </VListItemTitle>
            <VListItemSubtitle class="mt-1">
              <VBadge
                dot
                location="start center"
                offset-x="2"
                color="success"
                class="me-3"
              >
                <span class="ms-4">{{ mandat.pendaftaran ? readableFileSize(mandat.pendaftaran?.size) : '-' }}</span>
              </VBadge>

              <span class="text-xs text-disabled" />
            </VListItemSubtitle>

            <template #append>
              <VBtn
                v-if="mandat.pendaftaran"
                icon="tabler-trash"
                :disabled="configSystem.configSystem?.status_pendaftaran"
                rounded
                size="small"
                color="error"
                :loading="isLoading"
                @click="openConfirmDialog(mandat)"
              />
              <VBtn
                v-else
                size="small"
                icon="tabler-upload"
                :disabled="configSystem.configSystem?.status_pendaftaran"
                rounded
                color="primary"
                :loading="isLoading"
                @click="openUploadDialog(mandat)"
              />
            </template>
          </VListItem>
        </template>
      </VList>
    </VCol>
  </VCard>

  <UploadMandat
    v-model:isOpenDialogVisible="isOpenDialogVisible"
    :is-mandat="isMandat"
    @refetch="getMandat"
  />
  <!-- 👉 Confirm Dialog -->
  <ConfirmDialog
    v-model:isDialogVisible="isConfirmDialogVisible"
    cancel-title="Batal"
    confirm-title="Berhasil!"
    confirm-msg="Lampiran Mandat berhasil dihapus!."
    confirmation-question="Yakin ingin hapus Lampiran Mandat ?"
    cancel-msg="Batal hapus Lampiran Mandat!"
    @submit="deleteMandat"
  />
</template>

<style scoped lang="scss">
.drop-zone {
  border: 1px dashed rgba(var(--v-theme-on-surface), var(--v-border-opacity));
}
</style>
