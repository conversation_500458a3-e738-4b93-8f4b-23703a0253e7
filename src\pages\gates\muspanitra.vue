<script setup lang="ts">
import { useHead } from '@unhead/vue'
import AppBarSearch from '@/views/pages/gates/AppSearch.vue'

definePage({
  meta: {
    layout: 'blank',
    unauthenticatedOnly: true,
  },
})
useHead({
  title: 'Gate - Muspanitra',
})
</script>

<template>
  <div class="help-center-page">
    <div>
      <AppBarSearch title="SCAN QR CODE" subtitle="Enter" custom-class="rounded-0" />
    </div>
  </div>
</template>

<style lang="scss">
.help-center-page {
  /*
   * Menetapkan gambar latar belakang untuk halaman.
   * Menambahkan overlay warna hitam semi-transparan.
   */
  background-image: linear-gradient(rgba(95, 95, 95, 0.5), rgba(0, 0, 0, 0.5)), url('@images/pages/bg-login.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 100vh; // Pastikan div mengambil setidaknya tinggi viewport penuh

  .search-header {
    background-size: cover !important;
    padding-block-start: 7rem !important;
  }
}

@media (max-width: 960px) and (min-width: 800px) {
  .help-center-page {
    .v-container {
      padding-inline: 2rem !important;
    }

    .search-header {
      padding: 10rem !important;
    }
  }
}

@media (max-width: 599px) {
  .help-center-page {
    .search-header {
      padding-block-end: 2rem !important;
      padding-block-start: 4rem !important;
      padding-inline: 2rem !important;
    }
  }
}
</style>
