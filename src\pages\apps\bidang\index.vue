<script setup lang="ts">
import type { UserProperties } from '@db/apps/users/types'
import { useHead } from '@unhead/vue'
import { useNotyf } from '@/composables/useNotyf'
import { themeConfig } from '@themeConfig'
import AddNewBidangDrawer from '@/views/pages/apps/bidang/AddNewBidangDrawer.vue'

// 👉 Store
const searchQuery = ref('')
const selectedStatus = ref()
const notyf = useNotyf()
const isConfirmDialogVisible = ref(false)
const selected = ref<Bidang>()

// Data table options
const itemsPerPage = ref(10)
const page = ref(1)
const sortBy = ref()
const orderBy = ref()

interface Bidang {
  id?: string
  namaBidang: string
  isStatus: string
}

const dataBidang = ref<Bidang>()

// Update data table options
const updateOptions = (options: any) => {
  sortBy.value = options.sortBy[0]?.key
  orderBy.value = options.sortBy[0]?.order
}

// Headers
const headers = [
  { title: '#', key: 'index' },
  { title: 'Bidang', key: 'bidang', width: 400 },
  { title: 'Status', key: 'status', width: 400 },
  { title: 'Aksi', key: 'actions', sortable: false },
]

// 👉 Fetching users
const { data: bidangData, execute: fetchData } = await useApi<any>(createUrl('/bidang/list', {
  query: {
    q: searchQuery,
    status: selectedStatus,
    itemsPerPage,
    page,
    sortBy,
    orderBy,
  },
}))

const users = computed((): UserProperties[] => bidangData.value.content!.bidangs)
const totalData = computed(() => bidangData.value.content.total)

const resolveUserStatusVariant = (stat: string) => {
  const statLowerCase = stat.toLowerCase()
  if (statLowerCase === 'active')
    return 'success'

  if (statLowerCase === 'inactive')
    return 'secondary'

  return 'primary'
}

const isAddNewDivisiDrawerVisible = ref(false)

// 👉 Add new Divisi
const onFormSubmit = async (userData: Bidang) => {
  try {
    const form = new FormData()

    form.append('nama_bidang', userData.namaBidang)
    form.append('is_status', userData.isStatus)

    const res = await $api('/bidang/create', {
      method: 'POST',
      body: form,
      onResponseError({ response }) {
        notyf.dismissAll()
        notyf.error(response._data.message)
      },
    })

    notyf.dismissAll()
    notyf.success('Bidang berhasil ditambahkan')
    fetchData()
  }
  catch (err) {
    console.error(err)
  }
}

// 👉 Update new Divisi
const onFormUpdate = async (userData: Bidang) => {
  try {
    const form = new FormData()

    form.append('nama_bidang', userData.namaBidang)
    form.append('is_status', userData.isStatus)

    const res = await $api(`/bidang/update/${selected.value.id}`, {
      method: 'POST',
      body: form,
      onResponseError({ response }) {
        notyf.dismissAll()
        notyf.error(response._data.message)
      },
    })

    notyf.dismissAll()
    notyf.success('Bidang berhasil diupdate')
    await fetchData()
  }
  catch (err) {
    console.error(err)
  }
}

// 👉 Delete data
const deleteData = async (id: number) => {
  await $api(`/apps/users/${id}`, {
    method: 'DELETE',
  })

  // refetch User
  // TODO: Make this async
  await fetchData()
}

const openDeleteDataSelected = (row: any) => {
  selected.value = row
  isConfirmDialogVisible.value = true
}

const openEditDataSelected = (row: any) => {
  selected.value = row
  isAddNewDivisiDrawerVisible.value = true
}

watchEffect(() => {
  if (!isAddNewDivisiDrawerVisible.value)
    selected.value = null
})
useHead({
  title: `Data Bidang - ${themeConfig.app.title}`,
})
definePage({
  meta: {
    action: 'Dashboard',
    subject: 'dashboard',
  },
})
</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardText class="d-flex flex-wrap gap-4">
        <div class="me-3 d-flex gap-3">
          <AppSelect
            :model-value="itemsPerPage"
            :items="[
              { value: 10, title: '10' },
              { value: 25, title: '25' },
              { value: 50, title: '50' },
              { value: 100, title: '100' },
              { value: -1, title: 'All' },
            ]"
            style="inline-size: 6.25rem;"
            @update:model-value="itemsPerPage = parseInt($event, 10)"
          />
        </div>
        <VSpacer />

        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <!-- 👉 Search  -->
          <div style="inline-size: 15.625rem;">
            <AppTextField
              v-model="searchQuery"
              placeholder="Cari..."
            />
          </div>

          <!-- 👉 Add user button -->
          <VBtn
            prepend-icon="tabler-plus"
            @click="isAddNewDivisiDrawerVisible = true"
          >
            Baru
          </VBtn>
        </div>
      </VCardText>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :items="users"
        :items-length="totalData"
        :headers="headers"
        class="text-no-wrap"
        show-select
        @update:options="updateOptions"
      >
        <!-- index -->
        <template #item.index="{ index }">
          <div class="d-flex align-center">
            <div class="d-flex flex-column">
              {{ Math.ceil(page * itemsPerPage - itemsPerPage + index + 1) }}
            </div>
          </div>
        </template>
        <!-- bidang -->
        <template #item.bidang="{ item }">
          <div class="text-body-1 text-high-emphasis text-capitalize">
            {{ item.nama_bidang }}
          </div>
        </template>

        <!-- Status -->
        <template #item.status="{ item }">
          <VChip
            :color="resolveUserStatusVariant(item.is_status)"
            size="small"
            label
            class="text-capitalize"
          >
            {{ item.is_status }}
          </VChip>
        </template>

        <!-- Actions -->
        <template #item.actions="{ item }">
          <IconBtn @click="openEditDataSelected(item)">
            <VIcon icon="tabler-pencil" />
          </IconBtn>
          <IconBtn @click="openDeleteDataSelected(item)">
            <VIcon icon="tabler-trash" />
          </IconBtn>

          <IconBtn>
            <VIcon icon="tabler-eye" />
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalData"
          />
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
    <!-- 👉 Add New User -->
    <AddNewBidangDrawer
      v-model:isDrawerOpen="isAddNewDivisiDrawerVisible"
      :data-selected="selected"
      @refetch="onFormSubmit"
      @form-update="onFormUpdate"
    />
    <!-- 👉 Confirm Dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isConfirmDialogVisible"
      cancel-title="Batal"
      confirm-title="Berhasil!"
      confirm-msg="Divisi berhasil dihapus!."
      confirmation-question="Yakin ingin hapus Divisi ini ?"
      cancel-msg="Divisi tidak jadi dihapus!"
      @submit="deleteData"
    />
  </section>
</template>
