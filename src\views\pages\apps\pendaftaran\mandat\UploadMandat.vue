<script setup lang="ts">
import { useDropZone, useFileDialog, useObjectUrl } from '@vueuse/core'
import { useNotyf } from '@/composables/useNotyf'

import { usePendaftaran } from '@/stores/pendaftaran'
import { useConfigSystem } from '@/stores/configSystem'

const props = withDefaults(defineProps<Props>(), {
  isOpenDialogVisible: undefined,

})

const emit = defineEmits<Emit>()
interface Props {
  isOpenDialogVisible: boolean
  isMandat: null
}

interface Emit {
  (e: 'refetch'): void
  (e: 'update:isOpenDialogVisible', val: boolean): void
}

const onFormReset = () => {
  // userData.value = structuredClone(toRaw(props.userData))

  emit('update:isOpenDialogVisible', false)
}

const closeOpenDialogVisible = (val: boolean) => {
  emit('update:isOpenDialogVisible', val)
}

const isPendaftaran = usePendaftaran()
const configSystem = useConfigSystem()

const fileRules = [requiredValidator,
  (fileList: FileList) => !fileList || !fileList.length || fileList[0].size < 5000000 || 'File maksimal 5 MB!']

const dropZoneRef = ref<HTMLDivElement>()
interface FileData {
  file: File
  url: string
}
const mandatMusda = ref()
const mandatMuspanitra = ref()
const notyf = useNotyf()
const isLoading = ref(false)
const isConfirmDialogVisible = ref(false)
const fileData = ref<FileData[] | any>([])
const { open, onChange } = useFileDialog({ multiple: false, accept: 'image/*, .pdf' })

function onDrop(DroppedFiles: File[] | null) {
  DroppedFiles?.forEach(file => {
    if (file.type.slice(0, 1) !== 'image/') {
      // eslint-disable-next-line no-alert
      alert('Only image files are allowed')
    }

    fileData.value.push({
      file,
      url: useObjectUrl(file).value ?? '',
    })
  },
  )
}

onChange((selectedFiles: any) => {
  if (!selectedFiles)
    return

  if (fileData.value.length < 1) {
    for (const file of selectedFiles) {
      if (file.size > 5000000) {
        notyf.dismissAll()
        notyf.error('Ukuran File terlalu besar')
      }
      else {
        fileData.value.push({
          file,
          url: useObjectUrl(file).value ?? '',
        })
      }
    }
  }
})

const onFormSubmit = async () => {
  try {
    isLoading.value = true

    const form = new FormData()

    form.append('mandat_id', props.isMandat!.id)
    form.append('is_musda', props.isMandat!.mandat === 'Musda' ? '1' : '0')
    form.append('lampiran_mandat', fileData.value[0].file)

    const res = await $api('/pendaftaran/create', {
      method: 'POST',
      body: form,
      onResponseError({ response }) {
        isLoading.value = false
        notyf.dismissAll()
        notyf.error(response._data.content[0].message!)
      },
    })

    isLoading.value = false
    notyf.dismissAll()
    notyf.success('Peserta berhasil ditambahkan')
    fileData.value = []
    await isPendaftaran.getPendaftaran()
    emit('refetch')
    emit('update:isOpenDialogVisible', false)
  }
  catch (err) {
    isLoading.value = false
    console.error(err)
  }
}

const resolveIcon = (extension: string) => {
  if (extension === 'jpg' || extension === 'jpeg')
    return 'tabler-file-type-jpg'
  if (extension === 'png')
    return 'tabler-file-type-png'

  return 'tabler-file-type-pdf'
}

const getFileExtension = (filename: string) => {
  return filename.split('.').pop()?.toLowerCase() || ''
}

const openConfirmDialog = () => {
  isConfirmDialogVisible.value = true
}

const deleteMandat = async () => {
  try {
    // isLoading.value = true

    await $api(`/pendaftaran/delete/${mandatMusda?.value.id}`, {
      method: 'POST',
      onResponseError({ response }) {
        isLoading.value = false
        notyf.dismissAll()
        notyf.error(response._data.content[0].message!)
      },
    })

    // isLoading.value = false

    // notyf.dismissAll()
    // notyf.success('Lampiran Mandat berhasil dihapus')
    await isPendaftaran.getPendaftaran()
  }
  catch (err) {
    isLoading.value = false
    console.error(err)
  }
}

useDropZone(dropZoneRef, onDrop)

watchEffect(() => {
  if (props.isOpenDialogVisible)
    fileData.value = []
})
</script>

<template>
  <VDialog :width="$vuetify.display.smAndDown ? 'auto' : 600" :model-value="props.isOpenDialogVisible" persistent
    no-click-animation :retain-focus="false" @update:model-value="isOpenDialogVisible">
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="closeOpenDialogVisible(false)" />

    <!-- 👉 Media -->
    <VCard>
      <VCardItem>
        <template #title>
          <h6 class="text-h6">
            Upload Mandat {{ props.isMandat?.mandat }}
          </h6>
        </template>
        <template #append>
          <span class="text-primary font-weight-medium text-sm cursor-pointer" />
        </template>
      </VCardItem>

      <VCardText>
        <div class="flex px-5 py-2">
          <div class="h-auto relative">
            <div ref="dropZoneRef" class="cursor-pointer" @click="() => open()">
              <div v-if="fileData.length === 0"
                class="d-flex flex-column justify-center align-center gap-y-2 pa-5 drop-zone rounded">
                <IconBtn variant="tonal" class="rounded-sm">
                  <VIcon icon="tabler-upload" />
                </IconBtn>

                <span class="text-disabled">Format .pdf, .jpeg, .jpg (Maksimal 5 mb)</span>

                <VBtn variant="tonal" size="small">
                  Pilih Dokumen
                </VBtn>
              </div>

              <div v-else class="d-flex flex-column justify-center align-center gap-y-2 pa-12 drop-zone rounded">
                <VRow class="match-height mx-auto justify-center">
                  <template v-for="(item, index) in fileData" :key="index">
                    <VCol cols="12">
                      <VCard :ripple="false" border>
                        <VCardText class="d-flex flex-column" @click.stop>
                          <div class="d-flex flex-column align-center">
                            <template v-if="item.file.type.startsWith('image/')">
                              <VImg :src="item.url" width="70px" height="70px" cover class="rounded mb-2" />
                            </template>
                            <template v-else>
                              <VIcon size="70" :icon="resolveIcon(getFileExtension(item.file.name))" color="primary"
                                class="mb-2" />
                            </template>
                            <div class="mt-2">
                              <span class="clamp-text text-wrap text-center d-block">
                                {{ item.file.name }}
                              </span>
                              <span class="clamp-text text-wrap text-center d-block text-disabled text-caption">
                                {{ (item.file.size / 1000).toFixed(2) }} KB
                              </span>
                            </div>
                          </div>
                        </VCardText>
                        <VCardActions>
                          <VBtn variant="text" size="small" block @click.stop="fileData.splice(index, 1)">
                            Remove File
                          </VBtn>
                        </VCardActions>
                      </VCard>
                    </VCol>
                  </template>
                </VRow>
              </div>
            </div>
          </div>
        </div>
      </VCardText>
      <!-- 👉 Submit and Cancel -->
      <VCol cols="12" class="d-flex flex-wrap justify-center mb-5">
        <VBtn type="submit" :loading="isLoading"
          :disabled="fileData.length < 1 || configSystem.configSystem?.status_pendaftaran" prepend-icon="tabler-upload"
          @click.prevent="onFormSubmit">
          Upload
        </VBtn>
      </VCol>
    </VCard>
  </VDialog>
</template>
