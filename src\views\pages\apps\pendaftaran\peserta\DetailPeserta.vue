<script setup lang="ts">
import avatar1 from '@images/avatars/default-avatar.png'
import { useNotyf } from '@/composables/useNotyf'

const props = withDefaults(defineProps<Props>(), {
  isDetailFormDialogVisible: undefined,
  userData: () => ({
    id: '',
    nama: '',
    email: '',
    alamat: '',
    role: '',
    no_hp: '',
    umur: '',
    jabatan_id: '',
    jabatan: '',
    avatar: '',
    lampiran_foto: '',
    lampiran_kta: '',
    tgl_lahir: '',
    jenis_kelamin: '',
  }),
})

const emit = defineEmits<Emit>()

const notyf = useNotyf()

interface UserData {
  id: string
  nama: string
  email: string
  alamat: string
  no_hp: string
  role: string
  umur: number
  jabatan_id: string
  jabatan: string
  avatar: string
  lampiran_foto: string
  lampiran_kta: string
  tgl_lahir: string
  jenis_kelamin: string
}

interface Props {
  userData?: UserData
  isDetailFormDialogVisible: boolean
}

interface Emit {
  (e: 'update:isDetailFormDialogVisible', val: boolean): void
}

const baseURL = import.meta.env.VITE_API_BASE_URL
const isLoading = ref(false)
const isLoading2 = ref(false)
const fileFoto = ref<File>()
const errors = ref()
const userData = ref<UserData>(structuredClone(toRaw(props.userData)))

watch(props, () => {
  userData.value = structuredClone(toRaw(props.userData))
})

const dialogModelValueUpdate = (val: boolean) => {
  emit('update:isDetailFormDialogVisible', val)
}

const defaultFoto = computed(() => {
  if (!userData.value.lampiran_foto)
    return avatar1
  else if (userData.value.avatar)
    return userData.value.avatar
  else
    return `${baseURL}/storage/uploads/photos/peserta/${userData.value.lampiran_foto}`
})

const downloadFile = async (isTipe: string) => {
  if (isTipe === 'foto')
    isLoading.value = true
  else
    isLoading2.value = true

  try {
    const res: Blob = await $api('/peserta/download', {
      method: 'GET',
      responseType: 'blob',
      params: {
        id: userData.value.id,
        tipe: isTipe,
      },
      onResponseError({ response }) {
        isLoading.value = false
        isLoading2.value = false
        errors.value = response._data.errors
      },
    })

    isLoading.value = false
    isLoading2.value = false

    const fileURL = window.URL.createObjectURL(new Blob([res]))
    const fileLink = document.createElement('a')

    fileLink.href = fileURL

    fileLink.setAttribute(
      'download',
      isTipe === 'kta' ? `${userData.value.lampiran_kta}` : `${userData.value.lampiran_foto}`,
    )
    document.body.appendChild(fileLink)
    fileLink.click()
  }
  catch (err) {
    isLoading.value = false
    isLoading2.value = false
    console.error(err)
  }
}

watchEffect(() => {
  // eslint-disable-next-line sonarjs/no-collapsible-if
  if (props.isDetailFormDialogVisible) {
    if (fileFoto.value)
      userData.value.avatar = ''
  }
})
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 1000"
    :model-value="props.isDetailFormDialogVisible"
    persistent
    no-click-animation
    :retain-focus="false"
    @update:model-value="dialogModelValueUpdate"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="dialogModelValueUpdate(false)" />

    <VCard>
      <!-- 👉 Title -->
      <h4 class="text-h4 text-center mb-0 mt-1">
        Detail Peserta
      </h4>
      <p class="text-body-1 text-center">
        {{ props.userData.jabatan }}
      </p>
      <VDivider class="mb-4" />
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            md="8"
          >
            <VRow>
              <!-- 👉 Nama -->
              <VCol
                cols="12"
                md="8"
              >
                <VRow no-gutters>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      Nama
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      : {{ userData.nama }}
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      Email
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      : {{ userData.email }}
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      No.HP
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      : {{ userData.no_hp }}
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      Jenis Kelamin
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      : {{ userData.jenis_kelamin === 'laki-laki' ? 'Laki-Laki' : 'Perempuan' }}
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      Alamat
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      : {{ userData.alamat }}
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      Tgl.Lahir
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      : {{ userData.tgl_lahir }}
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      Umur
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      : {{ userData.umur }} Tahun
                    </p>
                  </VCol>
                </VRow>
              </VCol>
            </VRow>
          </VCol>
          <VCol
            cols="12"
            md="4"
            class="text-center"
          >
            <p class="text-body-1 text-center">
              Foto
            </p>

            <!-- 👉 Avatar -->
            <VAvatar
              rounded
              size="100"
              class="d-flex align-center justify-center mx-auto mt-2 mb-5 border rounded"
              :image="defaultFoto"
            />

            <!-- 👉 Upload Photo -->
            <VBtn
              :loading="isLoading"
              :disabled="isLoading"
              color="secondary"
              size="small"
              class="mb-2"
              @click.prevent="downloadFile('foto')"
            >
              Download Foto
              <VIcon
                end
                icon="tabler-cloud-download"
              />
            </VBtn>
            <br>
            <VBtn
              :loading="isLoading2"
              :disabled="isLoading2"
              color="secondary"
              size="small"
              @click.prevent="downloadFile('kta')"
            >
              Download KTA
              <VIcon
                end
                icon="tabler-cloud-download"
              />
            </VBtn>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VDialog>
</template>
