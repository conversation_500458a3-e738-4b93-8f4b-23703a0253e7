<script setup lang="ts">
import { useTheme } from 'vuetify'
import ScrollToTop from '@core/components/ScrollToTop.vue'
import initCore from '@core/initCore'
import { initConfigStore, useConfigStore } from '@core/stores/config'
import { hexToRgb } from '@layouts/utils'
import { useIsLoader } from '@/stores/isLoader'
import { useConfigSystem } from '@/stores/configSystem'

const { global } = useTheme()
const isLoader = useIsLoader()
const configSystem = useConfigSystem()

// ℹ️ Sync current theme with initial loader theme
initCore()
initConfigStore()

const configStore = useConfigStore()

onMounted(() => {
  configSystem.setConfigSystem()
})
</script>

<template>
  <VLocaleProvider :rtl="configStore.isAppRTL">
    <!-- ℹ️ This is required to set the background color of active nav link based on currently active global theme's primary -->
    <VApp :style="`--v-global-theme-primary: ${hexToRgb(global.current.value.colors.primary)}`">
      <RouterView />
      <!-- Dialog -->
      <VDialog
        v-model="isLoader.isLoader"
        width="300"
        persistent
      >
        <VCard
          color="primary"
          width="300"
        >
          <VCardText class="pt-3">
            Loading...
            <VProgressLinear
              indeterminate
              bg-color="rgba(var(--v-theme-surface), 0.1)"
              :height="8"
              class="mb-0 mt-4"
            />
          </VCardText>
        </VCard>
      </VDialog>
      <ScrollToTop />
    </VApp>
  </VLocaleProvider>
</template>
