export default [
  {
    title: 'Dashboard',
    to: { name: 'apps-dashboard' },
    icon: { icon: 'tabler-brand-airtable' },
    action: 'Dashboard',
    subject: 'dashboard',
  },
  {
    title: 'Kwarcab',
    to: { name: 'apps-kwarcab' },
    icon: { icon: 'tabler-apps' },
    action: 'List User',
    subject: 'peserta.list',
  },
  {
    title: 'Peserta',
    icon: { icon: 'tabler-users' },
    action: 'List User',
    subject: 'peserta.list',
    children: [
      {
        title: 'Musda',
        to: { name: 'apps-peserta-musda' },
        action: 'List User',
        subject: 'peserta.list',
      },
      {
        title: 'Musda Kwarda',
        to: { name: 'apps-peserta-musda-kwarda' },
        action: 'List User',
        subject: 'peserta.list',
      },
      {
        title: 'Muspanitra',
        to: { name: 'apps-peserta-muspanitra' },
        action: 'List User',
        subject: 'peserta.list',
      },
    ],
  },
  {
    title: 'Panitia',
    icon: { icon: 'tabler-users-group' },
    action: 'List User',
    subject: 'peserta.list',
    children: [
      {
        title: 'Musda',
        to: { name: 'apps-panitia-musda' },
        action: 'List User',
        subject: 'peserta.list',
      },
      {
        title: 'Muspanitra',
        to: { name: 'apps-panitia-muspanitra' },
        action: 'List User',
        subject: 'peserta.list',
      },
    ],
  },

  // {
  //   title: 'Bidang',
  //   to: { name: 'apps-bidang' },
  //   icon: { icon: 'tabler-category-2' },
  //   action: 'List User',
  //   subject: 'peserta.list',
  // },

  // {
  //   title: 'Dashboard',
  //   to: { name: 'apps-pendaftaran' },
  //   icon: { icon: 'tabler-brand-airtable' },
  //   action: 'Create Pendaftaran',
  //   subject: 'pendaftaran.create',
  // },
]
