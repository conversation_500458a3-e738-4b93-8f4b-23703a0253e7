<script setup lang="ts">
import { VForm } from 'vuetify/components/VForm'
import AppSearchHeaderBg from '@images/pages/app-search-header-bg.png'
import { useNotyf } from '@/composables/useNotyf'
import avatar1 from '@images/avatars/default-avatar.png'

defineOptions({
  inheritAttrs: false,
})

const props = withDefaults(defineProps<Props>(), {
  density: 'comfortable',
  isReverse: false,
})

const baseURL = import.meta.env.VITE_API_BASE_URL

const resolveAvatar = (foto: string | undefined) => {
  if (foto != null)
    return foto

  return avatar1
}

interface UserData {
  id?: string
  qr_code?: string
  nama?: string
  lampiran_foto?: string
  kwarcab?: string
  jabatan?: string
  is_status: string
}

const userData = ref<UserData>()
const isLoading = ref(false)
const notyf = useNotyf()
const qrCode = ref('')
const refVForm = ref<VForm>()
const dataCount = ref([])
const errors = ref()
const isStatus = ref('musda')
interface Props {
  title?: string
  subtitle?: string
  customClass?: string
  placeholder?: string
  density?: 'comfortable' | 'compact' | 'default'
  isReverse?: boolean
}

const scaneQR = async () => {
  isLoading.value = true
  try {
    const res = await $api('/gates/scanner', {
      method: 'POST',
      body: {
        qr_code: qrCode.value,
      },
      onResponseError({ response }) {
        isLoading.value = false
        notyf.dismissAll()
        notyf.error(response._data.message)
        nextTick(() => {
          refVForm.value?.reset()
          refVForm.value?.resetValidation()
        })
      },
    })

    userData.value = res.content
    await getDataCount()
    await nextTick(() => {
      refVForm.value?.reset()
      refVForm.value?.resetValidation()
    })
  }
  catch (err) {
    console.error(err)
  }
}

// 👉 Update new Divisi

const getDataCount = async () => {
  try {
    const res = await $api('/gates/count', {
      params: {
        is_status: isStatus.value,
      },
      method: 'GET',
      onResponseError({ response }) {
        errors.value = response._data.errors
      },
    })

    dataCount.value = res.content
  }
  catch (err) {
    console.error(err)
  }
}

const onSubmit = () => {
  refVForm.value?.validate()
    .then(({ valid: isValid }) => {
      if (isValid)
        scaneQR()
    })
}

onMounted(() => {
  getDataCount()
})
</script>

<template>
  <!-- 👉 Search Banner  -->
  <VCard flat class="text-center search-header" :class="props.customClass">
    <VRow>
      <VCol cols="12" md="7">
        <div class="px-16">
          <VForm ref="refVForm" @submit.prevent="onSubmit">
            <VCardText>
              <slot name="title">
                <h4 class="text-h4 mb-2 font-weight-medium">
                  {{ props.title }}
                </h4>
              </slot>
              <div class="d-flex" :class="isReverse ? 'flex-column' : 'flex-column-reverse'">
                <p class="mb-0">
                  {{ props.subtitle }}
                </p>
                <!-- 👉 Search Input -->
                <div>
                  <AppTextField v-model="qrCode" v-bind="$attrs" class="search-header-input mx-auto my-4"
                    :placeholder="props.placeholder" :density="props.density" prepend-inner-icon="tabler-qrcode"
                    autofocus :rules="[requiredValidator]" />
                </div>
              </div>
            </VCardText>
          </VForm>
        </div>
      </VCol>
      <VCol cols="12" lg="4" sm="4">
        <div class="px-16">
          <VCard flat class="position-relative overflow-visible team-card mb-0">
            <div
              :style="{ maxHeight: '305px', minHeight: '230px', borderRadius: '10px 10px 10px 10px', backgroundColor: `'rgba(86, 202, 0, 0.16)'` }">
              <VImg height="230" class="team-image" :src="resolveAvatar(userData?.lampiran_foto)" />
            </div>
            <VCardText class="text-center pa-6"
              :style="{ border: `1px solid ${'rgba(86, 202, 0, 0.16)'}`, borderBlockStart: 'none', borderRadius: '0 0 6px 6px', boxSizing: 'border-box' }">
              <h5 class="text-h5">
                {{ userData?.nama }}
              </h5>
              <p class="text-body-1 text-disabled">
                {{ userData?.jabatan }}
              </p>
              <p class="text-body-1 text-disabled">
                {{ userData?.kwarcab }}
              </p>
            </VCardText>
          </VCard>
        </div>
      </VCol>
    </VRow>
    <VContainer class="mt-12">
      <VRow justify="center">
        <VCol v-for="(product, index) in dataCount" :key="index" cols="12" sm="2">
          <VCard flat :style="{ border: `1px solid ${product.color}` }" class="mx-4">
            <VCardText class="text-center">
              <h4 class="text-h4 font-weight-bold">
                {{ product.value }}
              </h4>
              <span class="text-body-1 font-weight-medium">{{ product.title }}</span>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </VContainer>
  </VCard>
</template>

<style lang="scss">
// Global override for white text in search input
.search-header-input input,
.search-header-input .v-field__input input,
.search-header-input .v-input__control input {
  color: white !important;
  -webkit-text-fill-color: white !important;
  caret-color: white !important;
}

.search-header {
  padding: 4rem !important;
  background: transparent !important;
}

// search input
.search-header-input {
  border-radius: 0.375rem !important;
  background-color: rgb(var(--v-theme-surface));
  max-inline-size: 28.125rem !important;
  text-align: center !important;
  color: white !important;

  // Input field styling - more specific selectors
  :deep(.v-field) {
    color: white !important;
  }

  :deep(.v-field__input) {
    color: white !important;
    font-weight: 500 !important;
    -webkit-text-fill-color: white !important;
  }

  :deep(.v-field__input input) {
    color: white !important;
    -webkit-text-fill-color: white !important;
  }

  // Placeholder styling
  :deep(.v-field__input::placeholder) {
    color: rgba(255, 255, 255, 0.7) !important;
    opacity: 1 !important;
  }

  :deep(.v-field__input input::placeholder) {
    color: rgba(255, 255, 255, 0.7) !important;
    opacity: 1 !important;
  }

  // Icon styling
  :deep(.v-field__prepend-inner) {
    color: white !important;
    opacity: 0.8;
  }

  :deep(.v-field__prepend-inner .v-icon) {
    color: white !important;
    opacity: 0.8;
  }

  // Field border styling
  :deep(.v-field__outline) {
    color: rgba(255, 255, 255, 0.3) !important;
  }

  :deep(.v-field__outline__start) {
    border-color: rgba(255, 255, 255, 0.3) !important;
  }

  :deep(.v-field__outline__end) {
    border-color: rgba(255, 255, 255, 0.3) !important;
  }

  // Focused state
  :deep(.v-field--focused .v-field__outline) {
    color: white !important;
  }

  :deep(.v-field--focused .v-field__outline__start) {
    border-color: white !important;
  }

  :deep(.v-field--focused .v-field__outline__end) {
    border-color: white !important;
  }

  // Label styling (if any)
  :deep(.v-label) {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  // Additional overrides for stubborn styles
  :deep(input) {
    color: white !important;
    -webkit-text-fill-color: white !important;
    caret-color: white !important;
  }

  :deep(.v-input__control) {
    color: white !important;
  }

  :deep(.v-input__slot) {
    color: white !important;
  }

  // Force override any theme colors
  :deep(.v-theme--light .v-field__input) {
    color: white !important;
  }

  :deep(.v-theme--dark .v-field__input) {
    color: white !important;
  }

  // Override autofill styles
  :deep(input:-webkit-autofill) {
    -webkit-text-fill-color: white !important;
    -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
    transition: background-color 5000s ease-in-out 0s !important;
  }

  :deep(input:-webkit-autofill:hover) {
    -webkit-text-fill-color: white !important;
  }

  :deep(input:-webkit-autofill:focus) {
    -webkit-text-fill-color: white !important;
  }

  :deep(input:-webkit-autofill:active) {
    -webkit-text-fill-color: white !important;
  }
}

@media (max-width: 37.5rem) {
  .search-header {
    padding: 1.5rem !important;
  }
}

.team-image {
  position: absolute;
  inset-block-start: 1.4rem;
  inset-inline: 0;
}

.headers {
  margin-block-end: 7rem;
}

.our-team {
  margin-block: 2.25rem;
}

.team-card {
  border-radius: 5px 5px 5px 5px;
}

.section-title::after {
  position: absolute;
  background: url('../../../assets/images/front-pages/icons/section-title-icon.png') no-repeat left bottom;
  background-size: contain;
  block-size: 100%;
  content: '';
  font-weight: 700;
  inline-size: 120%;
  inset-block-end: 0;
  inset-inline-start: 0%;
}
</style>
