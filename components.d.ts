/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AddAuthenticatorAppDialog: typeof import('./src/components/dialogs/AddAuthenticatorAppDialog.vue')['default']
    AddEditAddressDialog: typeof import('./src/components/dialogs/AddEditAddressDialog.vue')['default']
    AddEditPermissionDialog: typeof import('./src/components/dialogs/AddEditPermissionDialog.vue')['default']
    AddEditRoleDialog: typeof import('./src/components/dialogs/AddEditRoleDialog.vue')['default']
    AddPaymentMethodDialog: typeof import('./src/components/dialogs/AddPaymentMethodDialog.vue')['default']
    AppAutocomplete: typeof import('./src/@core/components/app-form-elements/AppAutocomplete.vue')['default']
    AppBarSearch: typeof import('./src/@core/components/AppBarSearch.vue')['default']
    AppCardActions: typeof import('./src/@core/components/cards/AppCardActions.vue')['default']
    AppCardCode: typeof import('./src/@core/components/cards/AppCardCode.vue')['default']
    AppCombobox: typeof import('./src/@core/components/app-form-elements/AppCombobox.vue')['default']
    AppDateTimePicker: typeof import('./src/@core/components/app-form-elements/AppDateTimePicker.vue')['default']
    AppDrawerHeaderSection: typeof import('./src/@core/components/AppDrawerHeaderSection.vue')['default']
    AppLoadingIndicator: typeof import('./src/components/AppLoadingIndicator.vue')['default']
    AppPricing: typeof import('./src/components/AppPricing.vue')['default']
    AppSearchHeader: typeof import('./src/components/AppSearchHeader.vue')['default']
    AppSelect: typeof import('./src/@core/components/app-form-elements/AppSelect.vue')['default']
    AppStepper: typeof import('./src/@core/components/AppStepper.vue')['default']
    AppTextarea: typeof import('./src/@core/components/app-form-elements/AppTextarea.vue')['default']
    AppTextField: typeof import('./src/@core/components/app-form-elements/AppTextField.vue')['default']
    BuyNow: typeof import('./src/@core/components/BuyNow.vue')['default']
    CardAddEditDialog: typeof import('./src/components/dialogs/CardAddEditDialog.vue')['default']
    CardStatisticsHorizontal: typeof import('./src/@core/components/cards/CardStatisticsHorizontal.vue')['default']
    CardStatisticsVertical: typeof import('./src/@core/components/cards/CardStatisticsVertical.vue')['default']
    CardStatisticsVerticalSimple: typeof import('./src/@core/components/CardStatisticsVerticalSimple.vue')['default']
    ConfirmDialog: typeof import('./src/components/dialogs/ConfirmDialog.vue')['default']
    CreateAppDialog: typeof import('./src/components/dialogs/CreateAppDialog.vue')['default']
    CustomCheckboxes: typeof import('./src/@core/components/app-form-elements/CustomCheckboxes.vue')['default']
    CustomCheckboxesWithIcon: typeof import('./src/@core/components/app-form-elements/CustomCheckboxesWithIcon.vue')['default']
    CustomCheckboxesWithImage: typeof import('./src/@core/components/app-form-elements/CustomCheckboxesWithImage.vue')['default']
    CustomizerSection: typeof import('./src/@core/components/CustomizerSection.vue')['default']
    CustomRadios: typeof import('./src/@core/components/app-form-elements/CustomRadios.vue')['default']
    CustomRadiosWithIcon: typeof import('./src/@core/components/app-form-elements/CustomRadiosWithIcon.vue')['default']
    CustomRadiosWithImage: typeof import('./src/@core/components/app-form-elements/CustomRadiosWithImage.vue')['default']
    DialogCloseBtn: typeof import('./src/@core/components/DialogCloseBtn.vue')['default']
    DropZone: typeof import('./src/@core/components/DropZone.vue')['default']
    EnableOneTimePasswordDialog: typeof import('./src/components/dialogs/EnableOneTimePasswordDialog.vue')['default']
    ErrorHeader: typeof import('./src/components/ErrorHeader.vue')['default']
    I18n: typeof import('./src/@core/components/I18n.vue')['default']
    MoreBtn: typeof import('./src/@core/components/MoreBtn.vue')['default']
    Notifications: typeof import('./src/@core/components/Notifications.vue')['default']
    PaymentProvidersDialog: typeof import('./src/components/dialogs/PaymentProvidersDialog.vue')['default']
    PricingPlanDialog: typeof import('./src/components/dialogs/PricingPlanDialog.vue')['default']
    ProductDescriptionEditor: typeof import('./src/@core/components/ProductDescriptionEditor.vue')['default']
    ReferAndEarnDialog: typeof import('./src/components/dialogs/ReferAndEarnDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollToTop: typeof import('./src/@core/components/ScrollToTop.vue')['default']
    ShareProjectDialog: typeof import('./src/components/dialogs/ShareProjectDialog.vue')['default']
    Shortcuts: typeof import('./src/@core/components/Shortcuts.vue')['default']
    TablePagination: typeof import('./src/@core/components/TablePagination.vue')['default']
    TheCustomizer: typeof import('./src/@core/components/TheCustomizer.vue')['default']
    ThemeSwitcher: typeof import('./src/@core/components/ThemeSwitcher.vue')['default']
    TiptapEditor: typeof import('./src/@core/components/TiptapEditor.vue')['default']
    TwoFactorAuthDialog: typeof import('./src/components/dialogs/TwoFactorAuthDialog.vue')['default']
    UserInfoEditDialog: typeof import('./src/components/dialogs/UserInfoEditDialog.vue')['default']
    UserUpgradePlanDialog: typeof import('./src/components/dialogs/UserUpgradePlanDialog.vue')['default']
  }
}
