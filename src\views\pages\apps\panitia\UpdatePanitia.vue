<script setup lang="ts">
import { VForm } from 'vuetify/components/VForm'

import { Indonesian } from 'flatpickr/dist/l10n/id'
import avatar1 from '@images/avatars/default-avatar.png'
import { useNotyf } from '@/composables/useNotyf'

const props = withDefaults(defineProps<Props>(), {
  isEditFormDialogVisible: undefined,
  panitiaData: () => ({
    id: '',
    nama: '',
    bidang_id: null,
    jabatan: '',
    avatar: '',
    lampiran_foto: '',
    is_status: '',
  }),
})

const emit = defineEmits<Emit>()

const notyf = useNotyf()

interface PanitiaData {
  id: string
  nama: string
  bidang_id: number
  jabatan: string
  avatar: string
  lampiran_foto: string
  is_status: string
}

interface Props {
  panitiaData?: PanitiaData
  isEditFormDialogVisible: boolean
  isMusda: boolean
}

interface Emit {
  (e: 'refetch'): void

  (e: 'update:isEditFormDialogVisible', val: boolean): void
}

const baseURL = import.meta.env.VITE_API_BASE_URL
const isFormValid = ref(false)
const isLoading = ref(false)
const refForm = ref<VForm>()
const fileFoto = ref<File>()
const errors = ref()
const panitiaData = ref<PanitiaData>(structuredClone(toRaw(props.panitiaData)))
const configs = ref({ dateFormat: 'Y-m-d', altFormat: 'd-m-Y', altInput: true, locale: Indonesian })
const bidangs = ref([])

const jabatans = [
  { title: 'Ketua', value: 'ketua' },
  { title: 'Wakil', value: 'wakil' },
  { title: 'Sekretaris', value: 'sekretaris' },
  { title: 'Bendahara', value: 'bendahara' },
  { title: 'Wakil Bendahara', value: 'wakil_bendahara' },
  { title: 'Anggota', value: 'anggota' },
]

const jabatanMuspanitra = [
  { title: 'Ketua', value: 'ketua' },
  { title: 'Sekretaris', value: 'sekretaris' },
  { title: 'Bendahara', value: 'bendahara' },
  { title: 'Bidang Kegiatan', value: 'bidang_kegiatan' },
  { title: 'Bidang Administrasi', value: 'bidang_administrasi' },
  { title: 'Bidang Sarana & Prasarana', value: 'bidang_sarana_prasarana' },
  { title: 'Bidang Humas', value: 'bidang_humas' },
]


const fileRules = [
  (fileList: FileList) => !fileList || !fileList.length || fileList[0].size < 5000000 || 'File maksimal 5 MB!',
]

watch(props, () => {
  panitiaData.value = structuredClone(toRaw(props.panitiaData))
})

const onFormReset = () => {
  panitiaData.value = structuredClone(toRaw(props.panitiaData))

  emit('update:isEditFormDialogVisible', false)
}

const dialogModelValueUpdate = (val: boolean) => {
  emit('update:isEditFormDialogVisible', val)
}

const defaultFoto = computed(() => {
  if (!panitiaData.value.lampiran_foto)
    return avatar1
  else if (panitiaData.value.avatar)
    return panitiaData.value.avatar
  else
    return `${baseURL}/storage/uploads/photos/${panitiaData.value.lampiran_foto}`
})

const getBidang = async () => {
  try {
    const res = await $api('/bidang/get', {
      params: {
        is_status: props.isMusda ? 'musda' : 'muspanitra',
      },
      method: 'GET',
      onResponseError({ response }) {
        errors.value = response._data.errors
      },
    })

    bidangs.value = res.content

    await nextTick(() => {
      // router.replace(route.query.to ? String(route.query.to) : '/')
    })
  }
  catch (err) {
    console.error(err)
  }
}

const onFormSubmit = async () => {
  try {
    isLoading.value = true

    const form = new FormData()

    form.append('nama', panitiaData.value.nama)
    form.append('bidang_id', panitiaData.value.bidang_id.toString())
    form.append('jabatan', panitiaData.value.jabatan)
    if (fileFoto.value)
      form.append('lampiran_foto', fileFoto.value[0])

    const res = await $api(`/panitia/update/${panitiaData.value.id}`, {
      method: 'POST',
      body: form,
      onResponseError({ response }) {
        isLoading.value = false
        notyf.dismissAll()
        notyf.error(response._data.content[0].message!)
      },
    })

    isLoading.value = false
    notyf.dismissAll()
    notyf.success('Panitia berhasil diupdate')
    emit('update:isEditFormDialogVisible', false)

    emit('refetch')
    await nextTick(() => {
      refForm.value?.reset()
      refForm.value?.resetValidation()
    })
  }
  catch (err) {
    isLoading.value = false
    console.error(err)
  }
}

const onSubmit = () => {
  refForm.value?.validate()
    .then(({ valid: isValid }) => {
      if (isValid)
        onFormSubmit()
    })
}

// changeAvatar function
const changeAvatar = (file: Event) => {
  const fileReader = new FileReader()
  const { files } = file.target as HTMLInputElement

  if (files && files.length) {
    fileReader.readAsDataURL(files[0])
    fileReader.onload = () => {
      if (typeof fileReader.result === 'string')
        panitiaData.value.avatar = fileReader.result
    }
  }
}

const checkDigit = (event: KeyboardEvent) => {
  if (event.key.length === 1 && Number.isNaN(Number(event.key)))
    event.preventDefault()
}

const ageCalculate = computed(() => {
  if (panitiaData.value.tgl_lahir) {
    const birthdateDate = new Date(panitiaData.value.tgl_lahir)
    const now = new Date()
    const diff = now.getTime() - birthdateDate.getTime()

    return Math.floor(diff / (1000 * 60 * 60 * 24 * 365.25))
  }
  else {
    return 0
  }
})

watchEffect(() => {
  if (props.isEditFormDialogVisible) {
    if (fileFoto.value)
      panitiaData.value.avatar = ''
    getBidang()
  }
})
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isEditFormDialogVisible"
    persistent
    no-click-animation
    :retain-focus="false"
    @update:model-value="dialogModelValueUpdate"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="dialogModelValueUpdate(false)" />

    <VCard>
      <VCardText>
        <!-- 👉 Title -->
        <h4 class="text-h4 text-center">
          Formulir Edit Panitia
        </h4>

        <VDivider class="mb-4" />
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          v-model="isFormValid"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol
              cols="12"
              md="8"
            >
              <!-- 👉 Nama -->
              <VCol
                cols="12"
                md="8"
              >
                <AppTextField
                  v-model="panitiaData.nama"
                  label="Nama"
                  placeholder="John"
                  :rules="[requiredValidator]"
                />
              </VCol>

              <!-- 👉 Jenis Bidang -->
              <VCol
                cols="12"
                md="8"
              >

              
                <AppSelect
                  v-model="panitiaData.bidang_id"
                  :items="bidangs"
                  label="Bidang Kepanitian"
                  placeholder="Pilih"
                  :rules="[requiredValidator]"
                />
              </VCol>

              <!-- 👉 Jabatan -->
              <VCol
                cols="12"
                md="8"
              >
                <AppSelect
                  v-model="panitiaData.jabatan"
                  label="Jabatan Kepanitian"
                  placeholder="-"
                  :items="isMusda ? jabatans : jabatanMuspanitra"
                  :rules="[requiredValidator]"
                />
                
              </VCol>
            </VCol>
            <VCol
              cols="12"
              md="4"
              class="text-center"
            >
              <p class="text-body-1 text-center">
                Foto
              </p>

              <!-- 👉 Avatar -->
              <VAvatar
                rounded
                size="100"
                class="d-flex align-center justify-center mx-auto mt-2 mb-5 border rounded"
                :image="defaultFoto"
              />
              <VAlert
                color="primary"
                variant="outlined"
                class="gap-2"
              >
                <span class="text-body-2 mb-2 text-center">
                  Hanya JPG, JPEG. Maksimal size 5MB. Pastikan foto tidak terbalik.
                </span>
              </VAlert>
              <!-- 👉 Upload Photo -->
              <div class="d-flex flex-column justify-center gap-5 mt-2">
                <VFileInput
                  v-model="fileFoto"
                  :rules="fileRules"
                  placeholder="Upload your documents"
                  label="Upload Foto"
                  prepend-icon="tabler-camera"
                  accept=".jpg, .jpeg"
                  @input="changeAvatar"
                />
              </div>
            </VCol>
          </VRow>
          <!-- 👉 Submit and Cancel -->
          <VCol
            cols="12"
            class="d-flex flex-wrap justify-center gap-4 mt-5"
          >
            <VBtn
              type="submit"
              :loading="isLoading"
              prepend-icon="tabler-device-floppy"
            >
              Update
            </VBtn>

            <VBtn
              color="secondary"
              variant="tonal"
              prepend-icon="tabler-square-rounded-x"
              @click="onFormReset"
            >
              Batal
            </VBtn>
          </VCol>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
