<script setup lang="ts">
import AddNewPeserta from '@/views/pages/apps/pendaftaran/peserta/AddNewPeserta.vue'
import { useNotyf } from '@/composables/useNotyf'
import avatar1 from '@images/avatars/default-avatar.png'
import UpdatePeserta from '@/views/pages/apps/pendaftaran/peserta/UpdatePeserta.vue'
import DetailPeserta from '@/views/pages/apps/pendaftaran/peserta/DetailPeserta.vue'
import { useConfigSystem } from '@/stores/configSystem'
import { usePendaftaran } from '@/stores/pendaftaran'

const getDataPeserta = usePendaftaran()
const getPendaftaran = usePendaftaran()
const configSystem = useConfigSystem()
const isLoading = ref(false)
const isFormDialogVisible = ref(false)
const isEditFormDialogVisible = ref(false)
const isDetailFormDialogVisible = ref(false)
const isConfirmDialogVisible = ref(false)
const searchQuery = ref('')
const selectedStatus = ref()
const selectedData = ref()
const notyf = useNotyf()
const baseURL = import.meta.env.VITE_API_BASE_URL
const pendaftaran = ref(true)
const isMusda = ref(false)

// Data table options
const itemsPerPage = ref(10)
const page = ref(1)
const sortBy = ref()
const orderBy = ref()

interface Status {
  Verified: string
  Rejected: string
  Pending: string
}

interface Peserta {
  id: string
  nomor_peserta: string
  nama: string
  no_hp: string
  lampiran_foto: string
  jabatan: string
  trend: string
}

const resolveStatus: Status = {
  Verified: 'success',
  Rejected: 'error',
  Pending: 'secondary',
}

const getPaddingStyle = (index: number) => index ? 'padding-block-end: 1.5rem;' : 'padding-block: 1.5rem;'

// 👉 Fetching users
const { data: pesertaData, execute: fetchData } = await useApi<any>(createUrl('/jabatan/list', {
  query: {
    q: searchQuery,
    status: selectedStatus,
    itemsPerPage,
    page,
    sortBy,
    orderBy,
    pendaftaran: pendaftaran.value,
    musda: false,
  },
}))

const jabatans = computed((): Peserta[] => pesertaData.value.content!.jabatans)
const totalData = computed(() => pesertaData.value?.content!.total)

const resolveAvatar = (foto: string) => {
  if (foto !== '')
    return `${baseURL}/storage/uploads/photos/peserta/${foto}`

  return avatar1
}

const openAddForm = (row: any) => {
  isFormDialogVisible.value = true
  selectedData.value = row
}

const openEditForm = (row: any) => {
  isEditFormDialogVisible.value = true
  selectedData.value = row?.peserta
}

const openDetailForm = (row: any) => {
  isDetailFormDialogVisible.value = true
  selectedData.value = row.peserta
}

const openDeletePesertaSelected = (row: any) => {
  isConfirmDialogVisible.value = true
  selectedData.value = row.peserta
}

interface Status {
  'Online': string
  'Away': string
  'Offline': string
  'In Meeting': string
}

interface Users {
  avatar: string
  name: string
  status: keyof Status
  lastVisited: string
}

const users: Users[] = [
  {
    avatar: avatar1,
    name: 'Caroline Black',
    status: 'Online',
    lastVisited: '13 minutes ago',
  },
]

const resolveStatusColor: Status = {
  'Online': 'success',
  'Away': 'warning',
  'Offline': 'secondary',
  'In Meeting': 'error',
}

const deletePeserta = async () => {
  try {
    await $api(`/peserta/delete/${selectedData.value.id}`, {
      method: 'POST',
      onResponseError({ response }) {
        notyf.dismissAll()
        notyf.error(response._data.content[0].message!)
      },
    })
    await fetchData()
    await getDataPeserta.getPendaftaran()
    await getDataPeserta.getDataPeserta()
  }
  catch (err) {
    console.error(err)
  }
}

const checkMandat = computed(() => {
  if (getPendaftaran.pendaftaran)
    return getPendaftaran.pendaftaran[1]!.pendaftaran != null
})

onMounted(() => {
  fetchData()
})
</script>

<template>
  <VCard>
    <VTable class="text-no-wrap transaction-table">
      <thead>
        <tr>
          <th>#</th>
          <th>NAMA</th>
          <th>NO.HP</th>
          <th>JABATAN</th>
          <th class="text-center">
            AKSI
          </th>
        </tr>
      </thead>

      <tbody>
        <tr
          v-for="(jabatan, index) in jabatans"
          :key="index"
        >
          <td :style="getPaddingStyle(index)">
            <div class="d-flex align-center" />
            <div>
              <p class="text-body-2 text-base mb-0 text-high-emphasis">
                {{ index + 1 }}.
              </p>
            </div>
          </td>
          <td :style="getPaddingStyle(index)">
            <div class="d-flex align-center">
              <div class="me-4">
                <VAvatar
                  :image="resolveAvatar(jabatan.peserta?.lampiran_foto)"
                  width="50"
                />
              </div>
              <div>
                <p class="text-body-2 text-base mb-0 text-high-emphasis">
                  {{ jabatan.peserta?.nama ?? '-' }}
                </p>
              </div>
            </div>
          </td>
          <td :style="getPaddingStyle(index)">
            <div class="d-flex align-center" />
            <div>
              <p class="text-body-2 text-base mb-0 text-high-emphasis">
                {{ jabatan.peserta?.no_hp ?? '-' }}
              </p>
            </div>
          </td>

          <td :style="getPaddingStyle(index)">
            <div class="d-flex align-center" />
            <div>
              <p class="text-body-2 text-base mb-0 text-high-emphasis">
                {{ jabatan.nama_jabatan }}
              </p>
            </div>
          </td>
          <!-- Actions -->
          <td class="text-center">
            <div v-if="jabatan.peserta!">
              <IconBtn @click="openDetailForm(jabatan)">
                <VIcon icon="tabler-eye" />
              </IconBtn>

              <IconBtn
                :disabled="configSystem.configSystem!.status_pendaftaran"
                @click="openEditForm(jabatan)"
              >
                <VIcon icon="tabler-pencil" />
              </IconBtn>
              <IconBtn
                :disabled="configSystem.configSystem!.status_pendaftaran"
                @click="openDeletePesertaSelected(jabatan)"
              >
                <VIcon icon="tabler-trash" />
              </IconBtn>
            </div>
            <VBtn
              v-else
              prepend-icon="tabler-plus"
              size="x-small"
              :disabled="configSystem.configSystem!.status_pendaftaran || !checkMandat"
              @click="openAddForm(jabatan)"
            >
              Add
            </VBtn>
          </td>
        </tr>
      </tbody>
    </VTable>
  </VCard>
  <AddNewPeserta
    v-model:isFormDialogVisible="isFormDialogVisible"
    :jabatan-selected="selectedData"
    :is-musda="isMusda"
    @refetch="fetchData"
  />
  <UpdatePeserta
    v-model:isEditFormDialogVisible="isEditFormDialogVisible"
    :user-data="selectedData"
    @refetch="fetchData"
  />
  <DetailPeserta
    v-model:isDetailFormDialogVisible="isDetailFormDialogVisible"
    :user-data="selectedData"
    @refetch="fetchData"
  />
  <!-- 👉 Confirm Dialog -->
  <ConfirmDialog
    v-model:isDialogVisible="isConfirmDialogVisible"
    cancel-title="Batal"
    confirm-title="Berhasil!"
    confirm-msg="Peserta berhasil dihapus!."
    confirmation-question="Yakin ingin hapus Peserta ?"
    cancel-msg="Batal hapus Peserta!"
    @submit="deletePeserta"
  />
</template>

<style lang="scss">
.transaction-table {
  &.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > td,
  &.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > th {
    border-block-end: none !important;
  }
}
</style>
