<script setup lang="ts">
import avatar1 from '@images/avatars/default.png'
import { useIsLoader } from '@/stores/isLoader'

const isLoader = useIsLoader()
const ability = useAbility()
const route = useRoute()
const router = useRouter()
const isLoading = ref(false)
const userData = ref(useCookie('userData').value)

const logout = async () => {
  try {
    const res = await $api('/user/logout', {
      method: 'POST',
      onResponseError({ response }) {

      },
    })

    useCookie('userAbilityRules').value = null
    ability.update([])

    useCookie('userData').value = null
    useCookie('accessToken').value = null

    // Redirect to `to` query if exist or redirect to index route
    // ❗ nextTick is required to wait for DOM updates and later redirect
    await nextTick(() => {
      router.replace(route.query.to ? String(route.query.to) : '/')
    })
  }
  catch (err) {
    console.error(err)
  }
}

const startLogout = () => {
  isLoader.setIsLoader(true)
  setTimeout(() => {
    isLoader.setIsLoader(false)
    logout()
  }, 2000)
}
</script>

<template>
  <VBadge
    dot
    location="bottom right"
    offset-x="3"
    offset-y="3"
    bordered
    color="success"
  >
    <VAvatar
      class="cursor-pointer"
      color="primary"
      variant="tonal"
    >
      <VImg :src="avatar1" />

      <!-- SECTION Menu -->
      <VMenu
        activator="parent"
        width="230"
        location="bottom end"
        offset="14px"
      >
        <VList>
          <!-- 👉 User Avatar & Name -->
          <VListItem>
            <template #prepend>
              <VListItemAction start>
                <VBadge
                  dot
                  location="bottom right"
                  offset-x="3"
                  offset-y="3"
                  color="success"
                >
                  <VAvatar
                    color="primary"
                    variant="tonal"
                  >
                    <VImg
                      v-if="avatar1"
                      :src="avatar1"
                    />
                  </VAvatar>
                </VBadge>
              </VListItemAction>
            </template>

            <VListItemTitle class="font-weight-semibold">
              {{ userData!.full_name }}
            </VListItemTitle>
            <VListItemSubtitle> {{ userData!.role.toUpperCase() }}</VListItemSubtitle>
          </VListItem>

          <VDivider class="my-2" />

          <!-- 👉 Profile -->
          <VListItem link>
            <template #prepend>
              <VIcon
                class="me-2"
                icon="tabler-user"
                size="22"
              />
            </template>

            <VListItemTitle>Profile</VListItemTitle>
          </VListItem>

          <!-- Divider -->
          <VDivider class="my-2" />

          <!-- 👉 Logout -->
          <VListItem
            :loading="isLoading"
            @click.prevent="startLogout"
          >
            <template #prepend>
              <VIcon
                class="me-2"
                icon="tabler-logout"
                size="22"
              />
            </template>

            <VListItemTitle>Logout</VListItemTitle>
          </VListItem>
        </VList>
      </VMenu>
      <!-- !SECTION -->
    </VAvatar>
  </VBadge>
</template>
