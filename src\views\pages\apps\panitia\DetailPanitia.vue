<script setup lang="ts">
import avatar1 from '@images/avatars/default-avatar.png'

const props = withDefaults(defineProps<Props>(), {
  isDetailFormDialogVisible: undefined,
  panitiaData: () => ({
    id: '',
    nomor_panitia: '',
    nama: '',
    bidang: '',
    jabatan: '',
    avatar: '',
    lampiran_foto: '',
  }),
})

const emit = defineEmits<Emit>()

interface PanitiaData2 {
  id: string
  nomor_panitia: string
  nama: string
  bidang: string
  avatar: string
  lampiran_foto: string
  jabatan: string
}

interface Props {
  panitiaData?: PanitiaData2
  isDetailFormDialogVisible: boolean
}

type Emit = (e: 'update:isDetailFormDialogVisible', val: boolean) => void

const baseURL = import.meta.env.VITE_API_BASE_URL
const isLoading = ref(false)
const errors = ref()
const panitiaData = ref<PanitiaData2>(structuredClone(toRaw(props.panitiaData)))

watch(props, () => {
  panitiaData.value = structuredClone(toRaw(props.panitiaData))
})

const dialogModelValueUpdate = (val: boolean) => {
  emit('update:isDetailFormDialogVisible', val)
}

const defaultFoto = computed(() => {
  if (!panitiaData.value.lampiran_foto)
    return avatar1
  else if (panitiaData.value.avatar)
    return panitiaData.value.avatar
  else
    return `${baseURL}/storage/uploads/photos/panitia/${panitiaData.value.lampiran_foto}`
})

const downloadFile = async () => {
  isLoading.value = true

  try {
    const res: Blob = await $api('/peserta/download', {
      method: 'GET',
      responseType: 'blob',
      params: {
        id: panitiaData.value.id,
      },
      onResponseError({ response }) {
        isLoading.value = false

        errors.value = response._data.errors
      },
    })

    isLoading.value = false

    const fileURL = window.URL.createObjectURL(new Blob([res]))
    const fileLink = document.createElement('a')

    fileLink.href = fileURL

    fileLink.setAttribute(
      'download',
      panitiaData.value.lampiran_foto,
    )
    document.body.appendChild(fileLink)
    fileLink.click()
  }
  catch (err) {
    isLoading.value = false
    console.error(err)
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isDetailFormDialogVisible"
    :retain-focus="false"
    @update:model-value="dialogModelValueUpdate"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="dialogModelValueUpdate(false)" />

    <VCard>
      <!-- 👉 Title -->
      <h4 class="text-h4 text-center mb-0 mt-1">
        Detail Panitia
      </h4>
      <VDivider class="mb-4" />
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            md="8"
          >
            <VRow>
              <!-- 👉 Nama -->
              <VCol
                cols="12"
                md="8"
              >
                <VRow no-gutters>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      Nama
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      : {{ panitiaData.nama }}
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      Bidang
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      : {{ panitiaData.bidang }}
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      Jabatan
                    </p>
                  </VCol>
                  <VCol
                    cols="12"
                    md="6"
                  >
                    <p class="text-body-1">
                      : {{ panitiaData.jabatan }}
                    </p>
                  </VCol>
                </VRow>
              </VCol>
            </VRow>
          </VCol>
          <VCol
            cols="12"
            md="4"
            class="text-center"
          >
            <p class="text-body-1 text-center">
              Foto
            </p>

            <!-- 👉 Avatar -->
            <VAvatar
              rounded
              size="100"
              class="d-flex align-center justify-center mx-auto mt-2 mb-5 border rounded"
              :image="defaultFoto"
            />

            <!-- 👉 Upload Photo -->
            <VBtn
              :loading="isLoading"
              :disabled="isLoading"
              color="secondary"
              size="small"
              class="mb-2"
              @click.prevent="downloadFile"
            >
              Download Foto
              <VIcon
                end
                icon="tabler-cloud-download"
              />
            </VBtn>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VDialog>
</template>
