import type { Router } from 'vue-router'
import { canNavigate } from '@layouts/plugins/casl'
import { ability } from '@/plugins/casl/ability'
import { useConfigStore } from '@core/stores/config'

export const setupGuards = async (router: Router) => {
  const checkIsLoggedIn = !!(useCookie('userData').value && useCookie('accessToken').value)
  if (checkIsLoggedIn) {
    try {
      const res = await $api('/user/profile', {
        method: 'GET',
        onResponseError({ response }) {
          // Remove "userAbilities" from cookie
          useCookie('userAbilityRules').value = null
          useCookie('userData').value = null
          useCookie('accessToken').value = null

          // Reset ability to initial ability
          ability.update([])
          window.location.pathname = '/login'
        },
      })

      const { userData } = res.content

      useCookie('userAbilityRules').value = userData!.permissions
      ability.update(userData.permissions)

      useCookie('userData').value = userData
      useCookie('accessToken').value = res.content.accessToken

      const configStore = useConfigStore()

      configStore.appContentLayoutNav = res.content.layout
      localStorage.setItem('menu', res.content.layout)
    }
    catch (err) {
      // console.error(err)
      // Remove "userAbilities" from cookie
      useCookie('userAbilityRules').value = null
      useCookie('userData').value = null
      useCookie('accessToken').value = null
      window.location.pathname = '/login'
    }
  }

  // 👉 router.beforeEach
  // Docs: https://router.vuejs.org/guide/advanced/navigation-guards.html#global-before-guards
  router.beforeEach(to => {
    /*
     * If it's a public route, continue navigation. This kind of apps are allowed to visited by login & non-login users. Basically, without any restrictions.
     * Examples of public routes are, 404, under maintenance, etc.
     */
    if (to.meta.public)
      return

    /**
     * Check if user is logged in by checking if token & user data exists in local storage
     * Feel free to update this logic to suit your needs
     */
    const isLoggedIn = !!(useCookie('userData').value && useCookie('accessToken').value)

    /*
      If user is logged in and is trying to access login like page, redirect to home
      else allow visiting the page
      (WARN: Don't allow executing further by return statement because next code will check for permissions)
     */
    if (to.meta.unauthenticatedOnly) {
      if (isLoggedIn)

        return '/'
      else
        return undefined
    }

    if (!canNavigate(to) && to.matched.length) {
      return isLoggedIn
        ? { name: 'not-authorized' }
        : {
          name: 'login',
          query: {
            ...to.query,
            to: to.fullPath !== '/' ? to.path : undefined,
          },
        }
    }
  })
}
