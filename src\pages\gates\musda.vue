<script setup lang="ts">
import { useHead } from '@unhead/vue'
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useNotyf } from '@/composables/useNotyf'

// Interfaces
interface UserData {
  nama: string
  nomor_peserta: string
  utusan: string
  komisi: string
  jabatan: string
  kwarcab: string
  lampiran_foto: string | null
}

interface DataCount {
  peserta: number
  panitia: number
  total_scanned: number
}

interface Particle {
  id: number
  x: number
  y: number
  size: number
  color: string
  opacity: number
}


definePage({
  meta: {
    layout: 'blank',
    unauthenticatedOnly: true,
  },
})

useHead({
  title: 'Gate - Musda',
})

// Reactive data
const qrCode = ref('')
const isLoading = ref(false)
const userData = ref<UserData | null>(null)
const glitchActive = ref(false)
const scanLinePosition = ref(0)
const participantCount = ref(0)
const committeeCount = ref(0)
const totalScanned = ref(0)

// API related
const notyf = useNotyf()
const errors = ref()
const dataCount = ref<DataCount | null>(null)
const baseURL = import.meta.env.VITE_API_BASE_URL

// Digital Clock
const currentTime = ref('')
const currentDate = ref('')

// Time sequence for header
const timeSequence = ref([
  '04:00', '13:00', '01:00', '11:00', '23:00', '04:00', '13:00', '01:00',
  '11:00', '04:00', '13:00', '01:00', '23:00', '04:00', '13:00', '01:00',
  '11:00', '23:00', '04:00', '13:00', '01:00', '11:00', '23:00', '04:00'
])

// Neon particles
const neonParticles = ref<Particle[]>([])

// Form ref
const refVForm = ref()

// Functions
const scaneQR = async () => {
  isLoading.value = true
  try {
    const res = await $api('/gates/scanner', {
      method: 'POST',
      body: {
        is_musda: 'musda',
        qr_code: qrCode.value,
      },
      onResponseError({ response }) {
        isLoading.value = false
        notyf.dismissAll()
        notyf.error(response._data.message)
        nextTick(() => {
          refVForm.value?.reset()
          refVForm.value?.resetValidation()
        })
      },
    })

    userData.value = res.content
    await getDataCount()
    triggerGlitch()
    await nextTick(() => {
      refVForm.value?.reset()
      refVForm.value?.resetValidation()
    })
  }
  catch (err) {
    console.error(err)
  } finally {
    isLoading.value = false
  }
}

const getDataCount = async () => {
  try {
    const res = await $api('/gates/count', {
      params: {
        is_status: 'musda',
      },
      method: 'GET',
      onResponseError({ response }) {
        errors.value = response._data.errors
      },
    })

    dataCount.value = res.content

    // Update statistics from API response
    if (dataCount.value) {
      participantCount.value = dataCount.value.peserta || 0
      committeeCount.value = dataCount.value.panitia || 0
      totalScanned.value = dataCount.value.total_scanned || 0
    }
  }
  catch (err) {
    console.error(err)
  }
}

const onSubmit = () => {
  refVForm.value?.validate()
    .then(({ valid: isValid }) => {
      if (isValid)
        scaneQR()
    })
}

const resolveAvatar = (photo: string | null) => {
  if (photo && photo !== '')
    return `${baseURL}/storage/uploads/photos/peserta/${photo}`

  return 'https://via.placeholder.com/180'
}

const triggerGlitch = () => {
  glitchActive.value = true
  setTimeout(() => {
    glitchActive.value = false
  }, 500)
}

const initializeParticles = () => {
  const colors = ['#00ffcc', '#ff007f', '#00ff00', '#ff00ff', '#00bfff']
  neonParticles.value = Array.from({ length: 20 }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    size: Math.random() * 3 + 1,
    color: colors[Math.floor(Math.random() * colors.length)],
    opacity: Math.random() * 0.5 + 0.3
  }))
}

const animateScanLine = () => {
  const animate = () => {
    scanLinePosition.value = (scanLinePosition.value + 0.5) % 100
    requestAnimationFrame(animate)
  }
  animate()
}

const updateDigitalClock = () => {
  const now = new Date()

  // Format time (HH:MM:SS)
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  currentTime.value = `${hours}:${minutes}:${seconds}`

  // Format date (DD/MM/YYYY)
  const day = String(now.getDate()).padStart(2, '0')
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const year = now.getFullYear()
  currentDate.value = `${day}/${month}/${year}`
}

onMounted(() => {
  initializeParticles()
  animateScanLine()

  // Initialize and update digital clock
  updateDigitalClock()
  setInterval(updateDigitalClock, 1000)

  // Load initial data count
  getDataCount()

  // Trigger glitch effect periodically
  setInterval(() => {
    if (Math.random() < 0.1) {
      triggerGlitch()
    }
  }, 3000)
})
</script>

<template>
  <div class="futuristic-hud-interface">
    <!-- Background Grid -->
    <div class="hud-background">
      <div class="grid-overlay" />
      <div class="time-display">
        <!-- <div class="time-row">
          <span v-for="time in timeSequence" :key="time" class="time-item">{{ time }}</span>
        </div> -->
      </div>

      <!-- Digital Clock - Top Left (Safe from overlap) -->
      <div class="digital-clock">
        <div class="clock-container">
          <div class="clock-frame">
            <!-- Corner decorations -->
            <div class="clock-corner tl" />
            <div class="clock-corner tr" />
            <div class="clock-corner bl" />
            <div class="clock-corner br" />

            <!-- Clock content -->
            <div class="clock-content">
              <div class="clock-label">SYSTEM TIME</div>
              <div class="clock-time" :class="{ 'glitch-active': glitchActive }">
                {{ currentTime }}
                <span class="glitch-overlay">{{ currentTime }}</span>
                <span class="glitch-overlay">{{ currentTime }}</span>
              </div>
              <div class="clock-date">{{ currentDate }}</div>
              <div class="clock-status">
                <div class="status-dot online" />
                <span>SYNCHRONIZED</span>
              </div>
            </div>

            <!-- Side indicators -->
            <div class="clock-indicators">
              <div class="indicator" v-for="i in 3" :key="`clock-${i}`" />
            </div>
          </div>
        </div>
      </div>

      <!-- Neon Particles -->
      <div class="neon-particles">
        <div
          v-for="particle in neonParticles"
          :key="particle.id"
          class="particle"
          :style="{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
            opacity: particle.opacity,
            boxShadow: `0 0 ${particle.size * 4}px ${particle.color}`,
          }"
        />
      </div>
    </div>

    <!-- Main Title -->
    <div class="main-title-section">
      <h1 class="futuristic-title" :class="{ 'glitch-active': glitchActive }">
        GATE
        <span class="glitch-overlay">GATE</span>
        <span class="glitch-overlay">GATE</span>
      </h1>
      <h2 class="subtitle">
         <span class="highlight-cyan">MUSDA KWARTIR DAERAH ACEH</span>
      </h2>
    </div>

    <!-- Main HUD Layout -->
    <div class="hud-layout mt-5">
      <!-- Left Side - Main Scanner Frame -->
      <div class="left-section">
        <div class="main-scanner-frame">
          <!-- Corner Elements -->
          <div class="corner-element top-left" />
          <div class="corner-element top-right" />
          <div class="corner-element bottom-left" />
          <div class="corner-element bottom-right" />

          <!-- Side Indicators -->
          <div class="side-indicators left">
            <div class="indicator-item" v-for="i in 4" :key="`left-${i}`">
              <div class="indicator-dot" />
              <div class="indicator-line" />
            </div>
          </div>

          <div class="side-indicators right">
            <div class="indicator-item" v-for="i in 4" :key="`right-${i}`">
              <div class="indicator-dot" />
              <div class="indicator-line" />
            </div>
          </div>

          <!-- Scanner Content -->
          <div class="scanner-content">
            <div class="scanner-header">
              <h3 class="scanner-title">QR CODE SCANNER</h3>
              <div class="status-indicator">
                <div class="status-dot active" />
                <span>ONLINE</span>
              </div>
            </div>

            <!-- QR Input Form -->
            <div class="qr-input-section">
              <VForm ref="refVForm" @submit.prevent="onSubmit" class="hud-form">
                <div class="input-frame">
                  <AppTextField
                    v-model="qrCode"
                    class="hud-input white-text-input"
                    placeholder="ENTER QR CODE..."
                    variant="outlined"
                    prepend-inner-icon="tabler-qrcode"
                    autofocus
                    :rules="[requiredValidator]"
                    :loading="isLoading"
                    color="white"
                    base-color="white"
                    :style="{
                      '--v-field-input-color': '#ffffff',
                      '--v-input-color': '#ffffff',
                      '--v-theme-on-surface': '#ffffff',
                      color: '#ffffff'
                    }"
                  />

                  <VBtn
                    type="submit"
                    class="hud-button"
                    size="large"
                    :loading="isLoading"
                    block
                  >
                    <VIcon icon="tabler-scan" class="me-2" />
                    INITIATE SCAN
                  </VBtn>
                </div>
              </VForm>

              <!-- Instruction Text -->
              <p class="scanner-instruction">
                Masukkan kode QR untuk verifikasi sistem
              </p>
            </div>

            <!-- Scanner Grid -->
            <div class="scanner-grid">
              <div class="grid-line" v-for="i in 8" :key="`grid-${i}`" />
            </div>
          </div>

          <!-- Scan Line Animation -->
          <div class="scan-line-animation" :style="{ top: `${scanLinePosition}%` }" />
        </div>

        <!-- Warning Panel -->
        <div class="warning-panel">
          <div class="warning-stripes">
            <div class="stripe" v-for="i in 6" :key="`stripe-${i}`" />
          </div>
          <div class="warning-content">
            <VIcon icon="tabler-alert-triangle" class="warning-icon" />
            <span class="warning-text">WARNING</span>
          </div>
          <div class="warning-arrows">
            <div class="arrow" v-for="i in 8" :key="`arrow-${i}`" />
          </div>
          <div class="system-id">
            <span class="id-label">ID</span>
            <span class="id-number">{{ String(totalScanned).padStart(2, '0') }}</span>
          </div>
        </div>
      </div>

      <!-- Right Side - Data Panels -->
      <div class="right-section">
        <!-- Top Data Panel -->
        <div class="data-panel top-panel">
          <div class="panel-header">
            <div class="panel-corners">
              <div class="corner tl" />
              <div class="corner tr" />
              <div class="corner bl" />
              <div class="corner br" />
            </div>
            <div class="panel-title">USER PROFILE</div>
          </div>

          <div class="panel-content">
            <!-- Profile Display -->
            <div class="profile-display">
              <div class="profile-frame">
                <VImg
                  :src="userData ? resolveAvatar(userData.lampiran_foto) : 'https://via.placeholder.com/180'"
                  class="profile-image"
                  cover
                />
                <div class="profile-overlay" />
                <div class="profile-scan-lines" />
              </div>

              <!-- User Data -->
              <Transition name="data-fade" mode="out-in">
                <div v-if="userData" class="user-data">
                  <div class="data-row">
                    <span class="data-label">NAMA</span>
                    <span class="data-value">{{ userData.nama }}</span>
                  </div>
                  <div class="data-row">
                    <span class="data-label">NO. PESERTA</span>
                    <span class="data-value">{{ userData.nomor_peserta }}</span>
                  </div>
                  <div class="data-row">
                    <span class="data-label">UTUSAN</span>
                    <span class="data-value">{{ userData.utusan }}</span>
                  </div>
                  <div class="data-row">
                    <span class="data-label">KOMISI</span>
                    <span class="data-value">{{ userData.komisi }}</span>
                  </div>
                  <div class="data-row">
                    <span class="data-label">JABATAN</span>
                    <span class="data-value">{{ userData.jabatan }}</span>
                  </div>
                </div>
              </Transition>
            </div>
          </div>

          <div class="system-info">
            <span class="system-label">SYSTEM</span>
            <span class="system-status">ONLINE</span>
            <span class="system-version">v2.1</span>
          </div>
        </div>

        <!-- Bottom Data Panel -->
        <div class="data-panel bottom-panel">
          <div class="panel-header">
            <div class="panel-corners">
              <div class="corner tl" />
              <div class="corner tr" />
              <div class="corner bl" />
              <div class="corner br" />
            </div>
            <div class="panel-title">STATISTICS</div>
          </div>

          <div class="panel-content">
            <div class="stats-grid">
              <div class="stat-block">
                <div class="stat-number cyan">{{ participantCount }}</div>
                <div class="stat-label">PESERTA</div>
              </div>
              <div class="stat-block">
                <div class="stat-number magenta">{{ committeeCount }}</div>
                <div class="stat-label">PANITIA</div>
              </div>
              <div class="stat-block">
                <div class="stat-number cyan">{{ totalScanned }}</div>
                <div class="stat-label">SCANNED</div>
              </div>
            </div>
          </div>

          <div class="panel-indicators">
            <div class="indicator" v-for="i in 6" :key="`indicator-${i}`" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

// Global override for HUD input text color - Multiple approaches
.hud-input {
  // CSS Custom Properties
  --v-field-input-color: #ffffff;
  --v-input-color: #ffffff;
  --v-theme-on-surface: #ffffff;

  // Direct targeting
  input,
  .v-field__input input,
  .v-input__control input,
  .v-field__input,
  .v-input,
  .v-input__control,
  .v-input__slot {
    color: #ffffff !important;
    -webkit-text-fill-color: #ffffff !important;
    caret-color: #ffffff !important;
    font-family: 'Orbitron', monospace !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
  }

  // Vuetify specific overrides
  .v-field--variant-outlined .v-field__input {
    color: #ffffff !important;
  }

  .v-text-field input {
    color: #ffffff !important;
    -webkit-text-fill-color: #ffffff !important;
  }

  // Theme overrides
  &.v-theme--light,
  &.v-theme--dark {
    input,
    .v-field__input,
    .v-field__input input {
      color: #ffffff !important;
      -webkit-text-fill-color: #ffffff !important;
    }
  }
}

// Additional class for white text input - Most aggressive approach
.white-text-input {
  input {
    color: #ffffff !important;
    -webkit-text-fill-color: #ffffff !important;
    caret-color: #ffffff !important;
  }

  .v-field__input {
    color: #ffffff !important;

    input {
      color: #ffffff !important;
      -webkit-text-fill-color: #ffffff !important;
    }
  }

  // Override any Vuetify theme colors
  &.v-text-field {
    color: #ffffff !important;

    input {
      color: #ffffff !important;
      -webkit-text-fill-color: #ffffff !important;
    }
  }
}

// Ultra specific targeting for QR input
input[placeholder="ENTER QR CODE..."] {
  color: #ffffff !important;
  -webkit-text-fill-color: #ffffff !important;
  caret-color: #ffffff !important;
  font-family: 'Orbitron', monospace !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
}

// Target by class combination
.hud-input.white-text-input input,
.hud-input.white-text-input .v-field__input,
.hud-input.white-text-input .v-field__input input {
  color: #ffffff !important;
  -webkit-text-fill-color: #ffffff !important;
  caret-color: #ffffff !important;
  font-family: 'Orbitron', monospace !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
}

.futuristic-hud-interface {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: #00ffcc;
  font-family: 'Orbitron', monospace;
  position: relative;
  overflow: hidden;
  padding: 20px;

  // Background Grid
  .hud-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;

    .grid-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        linear-gradient(rgba(0, 255, 204, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 204, 0.1) 1px, transparent 1px);
      background-size: 50px 50px;
      animation: gridPulse 4s ease-in-out infinite;
    }

    .time-display {
      position: absolute;
      top: 20px;
      left: 0;
      right: 0;
      z-index: 2;

      .time-row {
        display: flex;
        justify-content: space-between;
        padding: 0 40px;

        .time-item {
          font-size: 10px;
          color: #00ffcc;
          opacity: 0.6;
          font-weight: 400;
          letter-spacing: 1px;
        }
      }
    }

    // Digital Clock - Top Left Position (Safe from overlap)
    .digital-clock {
      position: fixed;
      top: 30px;
      left: 30px;
      z-index: 1000;

      .clock-container {
        .clock-frame {
          position: relative;
          background: rgba(0, 20, 40, 0.9);
          border: 2px solid #00ffcc;
          border-radius: 8px;
          padding: 15px 20px;
          min-width: 200px;
          box-shadow:
            0 0 20px rgba(0, 255, 204, 0.4),
            inset 0 0 20px rgba(0, 255, 204, 0.1);

          // Corner decorations
          .clock-corner {
            position: absolute;
            width: 12px;
            height: 12px;
            border: 2px solid #ff007f;

            &.tl {
              top: -2px;
              left: -2px;
              border-right: none;
              border-bottom: none;
            }

            &.tr {
              top: -2px;
              right: -2px;
              border-left: none;
              border-bottom: none;
            }

            &.bl {
              bottom: -2px;
              left: -2px;
              border-right: none;
              border-top: none;
            }

            &.br {
              bottom: -2px;
              right: -2px;
              border-left: none;
              border-top: none;
            }
          }

          .clock-content {
            text-align: center;

            .clock-label {
              font-size: 0.7rem;
              color: #ffffff;
              opacity: 0.8;
              text-transform: uppercase;
              letter-spacing: 1px;
              margin-bottom: 5px;
              font-weight: 600;
            }

            .clock-time {
              font-size: 1.8rem;
              font-weight: 900;
              color: #00ffcc;
              text-shadow: 0 0 15px #00ffcc;
              margin-bottom: 5px;
              font-family: 'Orbitron', monospace;
              position: relative;

              &.glitch-active {
                animation: glitch 0.3s ease-in-out;
              }

              .glitch-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                opacity: 0;

                &:nth-child(2) {
                  color: #ff007f;
                  animation: glitch-1 0.3s ease-in-out;
                }

                &:nth-child(3) {
                  color: #ffff00;
                  animation: glitch-2 0.3s ease-in-out;
                }
              }
            }

            .clock-date {
              font-size: 0.9rem;
              color: #ffffff;
              opacity: 0.9;
              margin-bottom: 8px;
              font-weight: 600;
            }

            .clock-status {
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 6px;

              .status-dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;

                &.online {
                  background: #00ff00;
                  box-shadow: 0 0 8px #00ff00;
                  animation: pulse 1.5s ease-in-out infinite;
                }
              }

              span {
                font-size: 0.7rem;
                color: #00ff00;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
              }
            }
          }

          .clock-indicators {
            position: absolute;
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 5px;

            .indicator {
              width: 4px;
              height: 4px;
              background: #ff007f;
              border-radius: 50%;
              box-shadow: 0 0 6px #ff007f;
              animation: indicatorPulse 2s ease-in-out infinite;

              &:nth-child(2) {
                animation-delay: 0.5s;
              }

              &:nth-child(3) {
                animation-delay: 1s;
              }
            }
          }
        }
      }
    }

    // Responsive adjustments for digital clock (Top Left)
    @media (max-width: 768px) {
      .digital-clock {
        top: 20px;
        left: 20px;

        .clock-frame {
          padding: 10px 15px !important;
          min-width: 160px !important;

          .clock-time {
            font-size: 1.4rem !important;
          }

          .clock-label,
          .clock-date {
            font-size: 0.6rem !important;
          }
        }
      }
    }

    @media (max-width: 480px) {
      .digital-clock {
        top: 15px;
        left: 15px;

        .clock-frame {
          padding: 8px 12px !important;
          min-width: 140px !important;

          .clock-time {
            font-size: 1.2rem !important;
          }
        }
      }
    }
  }

  // Neon Particles
  .neon-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    pointer-events: none;

    .particle {
      position: absolute;
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;

      &:nth-child(odd) {
        animation-direction: reverse;
      }
    }
  }

  // Main Title Section
  .main-title-section {
    position: relative;
    z-index: 10;
    text-align: center;
    margin-top: 120px;
    margin-bottom: 40px;

    .futuristic-title {
      font-size: 2.5rem;
      font-weight: 900;
      color: #00ffcc;
      text-transform: uppercase;
      letter-spacing: 4px;
      margin-bottom: 10px;
      position: relative;
      text-shadow: 0 0 20px #00ffcc;

      &.glitch-active {
        animation: glitch 0.5s ease-in-out;
      }

      .glitch-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;

        &:nth-child(2) {
          color: #ff007f;
          animation: glitch-1 0.5s ease-in-out;
        }

        &:nth-child(3) {
          color: #ffff00;
          animation: glitch-2 0.5s ease-in-out;
        }
      }
    }

    .subtitle {
      font-size: 1.2rem;
      font-weight: 400;
      color: #ffffff;
      letter-spacing: 2px;

      .highlight-yellow {
        color: #ffff00;
        text-shadow: 0 0 10px #ffff00;
      }

      .highlight-cyan {
        color: #00ffcc;
        text-shadow: 0 0 10px #00ffcc;
      }
    }
  }

  // Main HUD Layout
  .hud-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
  }

  // Left Section - Scanner
  .left-section {
    .main-scanner-frame {
      position: relative;
      background: rgba(0, 20, 40, 0.8);
      border: 2px solid #00ffcc;
      border-radius: 10px;
      padding: 30px;
      box-shadow:
        0 0 30px rgba(0, 255, 204, 0.3),
        inset 0 0 30px rgba(0, 255, 204, 0.1);
      margin-bottom: 20px;

      // Corner Elements
      .corner-element {
        position: absolute;
        width: 20px;
        height: 20px;
        border: 2px solid #ff007f;

        &.top-left {
          top: -2px;
          left: -2px;
          border-right: none;
          border-bottom: none;
        }

        &.top-right {
          top: -2px;
          right: -2px;
          border-left: none;
          border-bottom: none;
        }

        &.bottom-left {
          bottom: -2px;
          left: -2px;
          border-right: none;
          border-top: none;
        }

        &.bottom-right {
          bottom: -2px;
          right: -2px;
          border-left: none;
          border-top: none;
        }
      }

      // Side Indicators
      .side-indicators {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);

        &.left {
          left: -15px;
        }

        &.right {
          right: -15px;
        }

        .indicator-item {
          margin-bottom: 20px;
          display: flex;
          align-items: center;

          .indicator-dot {
            width: 6px;
            height: 6px;
            background: #ff007f;
            border-radius: 50%;
            box-shadow: 0 0 10px #ff007f;
            animation: pulse 2s ease-in-out infinite;
          }

          .indicator-line {
            width: 15px;
            height: 1px;
            background: #ff007f;
            margin-left: 5px;
            opacity: 0.6;
          }
        }
      }

      // Scanner Content
      .scanner-content {
        .scanner-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;

          .scanner-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #00ffcc;
            text-shadow: 0 0 10px #00ffcc;
            margin: 0;
          }

          .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;

            .status-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;

              &.active {
                background: #00ff00;
                box-shadow: 0 0 10px #00ff00;
                animation: pulse 1s ease-in-out infinite;
              }
            }

            span {
              font-size: 0.9rem;
              color: #00ff00;
              font-weight: 600;
            }
          }
        }

        .qr-input-section {
          .hud-form {
            .input-frame {
              .hud-input {
                margin-bottom: 20px;

                :deep(.v-field) {
                  background: rgba(0, 255, 204, 0.1);
                  border: 1px solid #00ffcc;
                  border-radius: 8px;

                  &:hover {
                    border-color: #ff007f;
                    box-shadow: 0 0 15px rgba(255, 0, 127, 0.3);
                  }

                  &.v-field--focused {
                    border-color: #ff007f;
                    box-shadow: 0 0 20px rgba(255, 0, 127, 0.5);
                  }
                }

                // Multiple approaches to force white text
                :deep(.v-field__input) {
                  color: #ffffff !important;
                  font-family: 'Orbitron', monospace !important;
                  font-weight: 600 !important;
                  text-transform: uppercase !important;
                  letter-spacing: 1px !important;
                  -webkit-text-fill-color: #ffffff !important;
                }

                :deep(.v-field__input input) {
                  color: #ffffff !important;
                  -webkit-text-fill-color: #ffffff !important;
                  caret-color: #ffffff !important;
                  font-family: 'Orbitron', monospace !important;
                  font-weight: 600 !important;
                  text-transform: uppercase !important;
                  letter-spacing: 1px !important;
                }

                // Target all possible input elements
                :deep(input) {
                  color: #ffffff !important;
                  -webkit-text-fill-color: #ffffff !important;
                  caret-color: #ffffff !important;
                  font-family: 'Orbitron', monospace !important;
                  font-weight: 600 !important;
                  text-transform: uppercase !important;
                  letter-spacing: 1px !important;
                }

                // Override any theme colors
                :deep(.v-input) {
                  color: #ffffff !important;
                }

                :deep(.v-input__control) {
                  color: #ffffff !important;
                }

                :deep(.v-input__slot) {
                  color: #ffffff !important;
                }

                :deep(.v-field__prepend-inner) {
                  color: #00ffcc;
                }
              }

              .hud-button {
                background: linear-gradient(45deg, #ff007f, #ff00ff);
                color: #ffffff;
                font-family: 'Orbitron', monospace;
                font-weight: 700;
                text-transform: uppercase;
                letter-spacing: 2px;
                border: none;
                box-shadow: 0 0 20px rgba(255, 0, 127, 0.5);
                transition: all 0.3s ease;

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 5px 25px rgba(255, 0, 127, 0.7);
                }

                &:active {
                  transform: translateY(0);
                }
              }
            }
          }

          .scanner-instruction {
            text-align: center;
            color: #ffffff;
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 15px;
            font-style: italic;
          }
        }

        .scanner-grid {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;

          .grid-line {
            position: absolute;
            background: rgba(0, 255, 204, 0.2);

            &:nth-child(odd) {
              width: 100%;
              height: 1px;
              top: calc(12.5% * var(--i));
            }

            &:nth-child(even) {
              height: 100%;
              width: 1px;
              left: calc(12.5% * var(--i));
            }
          }
        }
      }

      // Scan Line Animation
      .scan-line-animation {
        position: absolute;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, transparent, #00ffcc, transparent);
        box-shadow: 0 0 10px #00ffcc;
        animation: scanMove 3s linear infinite;
      }
    }

    // Warning Panel
    .warning-panel {
      background: rgba(20, 0, 0, 0.8);
      border: 2px solid #ff0000;
      border-radius: 8px;
      padding: 15px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0 0 20px rgba(255, 0, 0, 0.3);

      .warning-stripes {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 40px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;

        .stripe {
          height: 3px;
          background: repeating-linear-gradient(
            45deg,
            #ff0000,
            #ff0000 5px,
            #ffff00 5px,
            #ffff00 10px
          );
          animation: stripeMove 1s linear infinite;
        }
      }

      .warning-content {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-left: 50px;

        .warning-icon {
          color: #ff0000;
          font-size: 1.5rem;
          animation: pulse 1s ease-in-out infinite;
        }

        .warning-text {
          color: #ff0000;
          font-weight: 700;
          font-size: 1.1rem;
          text-shadow: 0 0 10px #ff0000;
        }
      }

      .warning-arrows {
        display: flex;
        gap: 5px;

        .arrow {
          width: 0;
          height: 0;
          border-left: 8px solid #ff0000;
          border-top: 5px solid transparent;
          border-bottom: 5px solid transparent;
          animation: arrowPulse 0.5s ease-in-out infinite;

          &:nth-child(even) {
            animation-delay: 0.25s;
          }
        }
      }

      .system-id {
        display: flex;
        align-items: center;
        gap: 10px;

        .id-label {
          color: #ffffff;
          font-size: 0.9rem;
          opacity: 0.8;
        }

        .id-number {
          background: #ff007f;
          color: #ffffff;
          padding: 5px 10px;
          border-radius: 15px;
          font-weight: 700;
          font-size: 1rem;
          box-shadow: 0 0 10px #ff007f;
        }
      }
    }
  }

  // Right Section - Data Panels
  .right-section {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .data-panel {
      background: rgba(0, 20, 40, 0.8);
      border: 2px solid #00ffcc;
      border-radius: 10px;
      position: relative;
      box-shadow:
        0 0 30px rgba(0, 255, 204, 0.3),
        inset 0 0 30px rgba(0, 255, 204, 0.1);

      &.top-panel {
        flex: 1.2;
      }

      &.bottom-panel {
        flex: 0.8;
      }

      .panel-header {
        position: relative;
        padding: 15px 20px;
        border-bottom: 1px solid rgba(0, 255, 204, 0.3);

        .panel-corners {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;

          .corner {
            position: absolute;
            width: 15px;
            height: 15px;
            border: 2px solid #ff007f;

            &.tl {
              top: -2px;
              left: -2px;
              border-right: none;
              border-bottom: none;
            }

            &.tr {
              top: -2px;
              right: -2px;
              border-left: none;
              border-bottom: none;
            }

            &.bl {
              bottom: -2px;
              left: -2px;
              border-right: none;
              border-top: none;
            }

            &.br {
              bottom: -2px;
              right: -2px;
              border-left: none;
              border-top: none;
            }
          }
        }

        .panel-title {
          font-size: 1.2rem;
          font-weight: 700;
          color: #00ffcc;
          text-shadow: 0 0 10px #00ffcc;
          text-align: center;
          text-transform: uppercase;
          letter-spacing: 2px;
        }
      }

      .panel-content {
        padding: 30px;

        .profile-display {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 30px;

          .profile-frame {
            position: relative;
            width: 180px;
            height: 180px;
            border: 3px solid #ff007f;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 0 30px rgba(255, 0, 127, 0.6);

            .profile-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .profile-overlay {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: linear-gradient(
                45deg,
                rgba(0, 255, 204, 0.1) 0%,
                transparent 50%,
                rgba(255, 0, 127, 0.1) 100%
              );
            }

            .profile-scan-lines {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: repeating-linear-gradient(
                0deg,
                transparent,
                transparent 2px,
                rgba(0, 255, 204, 0.1) 2px,
                rgba(0, 255, 204, 0.1) 4px
              );
              animation: scanLines 2s linear infinite;
            }
          }

          .user-data {
            width: 100%;
            max-height: 300px;
            overflow-y: auto;

            .data-row {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 8px;
              padding: 6px 0;
              border-bottom: 1px solid rgba(0, 255, 204, 0.2);

              .data-label {
                font-size: 0.8rem;
                color: #ffffff;
                opacity: 0.8;
                font-weight: 600;
                min-width: 80px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
              }

              .data-value {
                font-size: 0.8rem;
                color: #00ffcc;
                font-weight: 700;
                text-shadow: 0 0 5px #00ffcc;
                text-align: right;
                flex: 1;
                margin-left: 10px;
                word-break: break-word;
              }
            }

            // Custom scrollbar for cyberpunk theme
            &::-webkit-scrollbar {
              width: 4px;
            }

            &::-webkit-scrollbar-track {
              background: rgba(0, 0, 0, 0.3);
              border-radius: 2px;
            }

            &::-webkit-scrollbar-thumb {
              background: #00ffcc;
              border-radius: 2px;
              box-shadow: 0 0 5px #00ffcc;
            }

            &::-webkit-scrollbar-thumb:hover {
              background: #ff007f;
              box-shadow: 0 0 8px #ff007f;
            }
          }
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 15px;

          .stat-block {
            text-align: center;
            padding: 15px 10px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 255, 204, 0.3);
            border-radius: 8px;

            .stat-number {
              font-size: 1.8rem;
              font-weight: 900;
              margin-bottom: 5px;

              &.cyan {
                color: #00ffcc;
                text-shadow: 0 0 15px #00ffcc;
              }

              &.magenta {
                color: #ff007f;
                text-shadow: 0 0 15px #ff007f;
              }
            }

            .stat-label {
              font-size: 0.8rem;
              color: #ffffff;
              opacity: 0.8;
              text-transform: uppercase;
              letter-spacing: 1px;
            }
          }
        }
      }

      .system-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 20px;
        border-top: 1px solid rgba(0, 255, 204, 0.3);
        font-size: 0.8rem;

        .system-label {
          color: #ffffff;
          opacity: 0.6;
        }

        .system-status {
          color: #00ff00;
          font-weight: 700;
          text-shadow: 0 0 5px #00ff00;
        }

        .system-version {
          color: #ff007f;
          font-weight: 700;
        }
      }

      .panel-indicators {
        display: flex;
        justify-content: center;
        gap: 8px;
        padding: 10px;

        .indicator {
          width: 8px;
          height: 8px;
          background: #00ffcc;
          border-radius: 50%;
          box-shadow: 0 0 8px #00ffcc;
          animation: indicatorPulse 2s ease-in-out infinite;

          &:nth-child(even) {
            animation-delay: 0.5s;
          }

          &:nth-child(3n) {
            animation-delay: 1s;
          }
        }
      }
    }
  }

  // Animations
  @keyframes gridPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  @keyframes glitch {
    0% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
    100% { transform: translate(0); }
  }

  @keyframes glitch-1 {
    0% { opacity: 0; transform: translate(0); }
    20% { opacity: 1; transform: translate(-2px, 2px); }
    40% { opacity: 0; transform: translate(-2px, -2px); }
    60% { opacity: 1; transform: translate(2px, 2px); }
    80% { opacity: 0; transform: translate(2px, -2px); }
    100% { opacity: 0; transform: translate(0); }
  }

  @keyframes glitch-2 {
    0% { opacity: 0; transform: translate(0); }
    25% { opacity: 1; transform: translate(2px, -2px); }
    50% { opacity: 0; transform: translate(-2px, 2px); }
    75% { opacity: 1; transform: translate(-2px, -2px); }
    100% { opacity: 0; transform: translate(0); }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.1); }
  }

  @keyframes scanMove {
    0% { top: 0%; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { top: 100%; opacity: 0; }
  }

  @keyframes stripeMove {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
  }

  @keyframes arrowPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
  }

  @keyframes scanLines {
    0% { transform: translateY(-100%); }
    100% { transform: translateY(100%); }
  }

  @keyframes indicatorPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.3; transform: scale(0.8); }
  }

  // Transitions
  .data-fade-enter-active,
  .data-fade-leave-active {
    transition: all 0.5s ease;
  }

  .data-fade-enter-from {
    opacity: 0;
    transform: translateY(20px);
  }

  .data-fade-leave-to {
    opacity: 0;
    transform: translateY(-20px);
  }

  // Responsive Design
  @media (max-width: 1200px) {
    .hud-layout {
      grid-template-columns: 1fr;
      gap: 30px;
    }

    .main-title-section {
      margin-top: 80px;

      .futuristic-title {
        font-size: 2rem;
      }
    }
  }

  @media (max-width: 768px) {
    .futuristic-hud-interface {
      padding: 10px;
    }

    .main-title-section {
      margin-top: 60px;
      margin-bottom: 20px;

      .futuristic-title {
        font-size: 1.5rem;
        letter-spacing: 2px;
      }

      .subtitle {
        font-size: 1rem;
      }
    }

    .hud-layout {
      gap: 20px;
    }

    .left-section {
      .main-scanner-frame {
        padding: 20px;
      }
    }

    .right-section {
      .data-panel {
        .panel-content {
          padding: 15px;

          .profile-display {
            gap: 15px;

            .profile-frame {
              width: 100px;
              height: 100px;
            }

            .user-data {
              max-height: 200px;

              .data-row {
                flex-direction: column;
                align-items: flex-start;
                margin-bottom: 10px;
                padding: 8px 0;

                .data-label {
                  font-size: 0.7rem;
                  margin-bottom: 2px;
                }

                .data-value {
                  font-size: 0.8rem;
                  text-align: left;
                  margin-left: 0;
                }
              }
            }
          }

          .stats-grid {
            grid-template-columns: 1fr;
            gap: 10px;
          }
        }
      }
    }

    .time-display {
      .time-row {
        padding: 0 20px;

        .time-item {
          font-size: 8px;
        }
      }
    }

    .digital-clock {
      top: 50px;
      right: 15px;

      .clock-container {
        .clock-frame {
          padding: 10px 15px;
          min-width: 160px;

          .clock-content {
            .clock-label {
              font-size: 0.6rem;
              margin-bottom: 3px;
            }

            .clock-time {
              font-size: 1.4rem;
              margin-bottom: 3px;
            }

            .clock-date {
              font-size: 0.8rem;
              margin-bottom: 5px;
            }

            .clock-status {
              gap: 4px;

              .status-dot {
                width: 5px;
                height: 5px;
              }

              span {
                font-size: 0.6rem;
              }
            }
          }

          .clock-indicators {
            right: -8px;
            gap: 3px;

            .indicator {
              width: 3px;
              height: 3px;
            }
          }
        }
      }
    }
  }
}</style>
