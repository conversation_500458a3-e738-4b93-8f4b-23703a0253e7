<script setup lang="ts">
import { useHead } from '@unhead/vue'
import { themeConfig } from '@themeConfig'
import PesertaMusda from '@/views/pages/apps/pendaftaran/peserta/PesertaMusda.vue'

import { usePendaftaran } from '@/stores/pendaftaran'
import PendaftaranDashboard from '@/views/pages/apps/pendaftaran/PendaftaranDashboard.vue'
import PesertaMuspanitra from '@/views/pages/apps/pendaftaran/peserta/PesertaMuspanitra.vue'
import MandatKwarcab from '@/views/pages/apps/pendaftaran/mandat/MandatKwarcab.vue'
import Panduan from '@/views/pages/apps/pendaftaran/Panduan.vue'

const isPendaftaran = usePendaftaran()
const userData = ref(useCookie('userData').value)

useHead({
  title: `Pendaftaran - ${themeConfig.app.title}`,
})
definePage({
  meta: {
    action: 'Create Pendaftaran',
    subject: 'pendaftaran.create',
  },
})

const tabsData = [
  { icon: 'tabler-home', title: 'dashboard' },
  { icon: 'tabler-file-certificate', title: 'Mandat' },
  { icon: 'tabler-users', title: 'Musda' },
  { icon: 'tabler-users', title: 'Muspanitra' },
  { icon: 'tabler-help', title: 'Panduan' },
]

const activeTab = ref(null)

onMounted(() => {
  isPendaftaran.getPendaftaran()
  isPendaftaran.getDataPeserta()
})
</script>

<template>
  <VRow>
    <VCol cols="12" md="3">
      <VCard class="mb-6 mr-2">
        <VCardItem>
          <h6 class="text-h6 text-left">
            KWARCAB
          </h6>
          <h5 class="text-h5 text-left mb-1">
            {{ userData!.kwarcab.toUpperCase() }}
          </h5>

          <VTabs v-model="activeTab" direction="vertical" class="v-tabs-pill disable-tab-transition">
            <VTab v-for="(tabItem, index) in tabsData" :key="index" :prepend-icon="tabItem.icon">
              {{ tabItem.title }}
            </VTab>
          </VTabs>
        </VCardItem>
      </vcard>
    </VCol>

    <VCol cols="12" md="8">
      <VWindow v-model="activeTab" class="disable-tab-transition" :touch="false">
        <VWindowItem>
          <PendaftaranDashboard />
        </VWindowItem>
        <VWindowItem>
          <MandatKwarcab />
        </VWindowItem>
        <VWindowItem>
          <PesertaMusda />
        </VWindowItem>
        <VWindowItem>
          <PesertaMuspanitra />
        </VWindowItem>
        <VWindowItem>
          <Panduan />
        </VWindowItem>
      </VWindow>
    </VCol>
  </VRow>
</template>

<style lang="scss">
.my-class {
  padding: 1.25rem;
  border-radius: 0.375rem;
  background-color: rgba(var(--v-theme-on-surface), var(--v-hover-opacity));
}
</style>
