import type { INotyfNotificationOptions, Notyf, NotyfNotification } from 'notyf'
import type { InjectionKey } from 'vue'

// import { useTheme } from 'vuetify'
// import { useThemeColors } from '/@src/composable/useThemeColors'

export const notyfSymbol: InjectionKey<Awaited<ReturnType<typeof initNotyfService>>>
  = Symbol('notyf')
export async function initNotyfService() {
  let notyf: Notyf

  if (!import.meta.env.SSR) {
    const { Notyf } = await import('notyf')

    notyf = new Notyf({
      duration: 2500,
      position: {
        x: 'right',
        y: 'top',
      },
      types: [
        {
          type: 'warning',
          background: 'orange',
          icon: {
            className: 'fas fa-hand-paper',
            tagName: 'i',
            text: '',
          },
        },
        {
          type: 'info',
          background: 'info',
          icon: {
            className: 'fa-info-circle',
            tagName: 'i',
            text: '',
          },
        },
        {
          type: 'primary',
          background: '#7367F0',
          icon: {
            className: 'fas fa-car-crash',
            tagName: 'i',
            text: '',
          },
        },
        {
          type: 'green',
          background: 'success',
          icon: {
            className: 'fas fa-check',
            tagName: 'i',
            text: '',
          },
        },

      ],
    })
  }

  return {
    dismiss: (notification: NotyfNotification) => {
      notyf?.dismiss(notification)
    },
    dismissAll: () => {
      notyf?.dismissAll()
    },
    success: (payload: string | Partial<INotyfNotificationOptions>) => {
      return notyf?.success(payload)
    },
    error: (payload: string | Partial<INotyfNotificationOptions>) => {
      return notyf?.error(payload)
    },
    info: (payload: string | Partial<INotyfNotificationOptions>) => {
      const options: Partial<INotyfNotificationOptions> = {
        type: 'info',
      }

      if (typeof payload === 'string')
        options.message = payload
      else
        Object.assign(options, payload)

      return notyf?.open(options)
    },
    warning: (payload: string | Partial<INotyfNotificationOptions>) => {
      const options: Partial<INotyfNotificationOptions> = {
        type: 'warning',
      }

      if (typeof payload === 'string')
        options.message = payload
      else
        Object.assign(options, payload)

      return notyf?.open(options)
    },
    primary: (payload: string | Partial<INotyfNotificationOptions>) => {
      const options: Partial<INotyfNotificationOptions> = {
        type: 'primary',
      }

      if (typeof payload === 'string')
        options.message = payload
      else
        Object.assign(options, payload)

      return notyf?.open(options)
    },
    purple: (payload: string | Partial<INotyfNotificationOptions>) => {
      const options: Partial<INotyfNotificationOptions> = {
        type: 'purple',
      }

      if (typeof payload === 'string')
        options.message = payload
      else
        Object.assign(options, payload)

      return notyf?.open(options)
    },
    blue: (payload: string | Partial<INotyfNotificationOptions>) => {
      const options: Partial<INotyfNotificationOptions> = {
        type: 'blue',
      }

      if (typeof payload === 'string')
        options.message = payload
      else
        Object.assign(options, payload)

      return notyf?.open(options)
    },
    green: (payload: string | Partial<INotyfNotificationOptions>) => {
      const options: Partial<INotyfNotificationOptions> = {
        type: 'green',
      }

      if (typeof payload === 'string')
        options.message = payload
      else
        Object.assign(options, payload)

      return notyf?.open(options)
    },
    orange: (payload: string | Partial<INotyfNotificationOptions>) => {
      const options: Partial<INotyfNotificationOptions> = {
        type: 'orange',
      }

      if (typeof payload === 'string')
        options.message = payload
      else
        Object.assign(options, payload)

      return notyf?.open(options)
    },
  }
}

export const notyf = initNotyfService()

// app.provide(notyfSymbol, notyf)
