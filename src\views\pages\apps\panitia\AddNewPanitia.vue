<script setup lang="ts">
import { VForm } from 'vuetify/components/VForm'
import { Indonesian } from 'flatpickr/dist/l10n/id'
import { usePendaftaran } from '@/stores/pendaftaran'
import avatar1 from '@images/avatars/default-avatar.png'
import { useNotyf } from '@/composables/useNotyf'

const props = withDefaults(defineProps<Props>(), {
  isFormDialogVisible: undefined,
  panitiaData: () => ({
    nama: '',
    jabatan: '',
    bindangId: '',
    avatar: '',
  }),
})

const emit = defineEmits<Emit>()
const getDataPeserta = usePendaftaran()

const notyf = useNotyf()

interface PanitiaData {
  nama: string
  jabatan: string
  bindangId: number
}

interface Jabatan {
  id: string
  nama_jabatan: string
}

interface Props {
  panitiaData?: PanitiaData
  isFormDialogVisible: boolean
  isMusda: boolean
  jabatanSelected?: Jabatan
}

interface Emit {
  (e: 'refetch'): void

  (e: 'update:isFormDialogVisible', val: boolean): void
}

const jabatans = [
  { title: 'Ketua', value: 'ketua' },
  { title: 'Wakil', value: 'wakil' },
  { title: 'Sekretaris', value: 'sekretaris' },
  { title: 'Bendahara', value: 'bendahara' },
  { title: 'Wakil Bendahara', value: 'wakil_bendahara' },
  { title: 'Anggota', value: 'anggota' },
]

const jabatanMuspanitra = [
  { title: 'Ketua', value: 'ketua' },
  { title: 'Sekretaris', value: 'sekretaris' },
  { title: 'Bendahara', value: 'bendahara' },
  { title: 'Bidang Kegiatan', value: 'bidang_kegiatan' },
  { title: 'Bidang Administrasi', value: 'bidang_administrasi' },
  { title: 'Bidang Sarana & Prasarana', value: 'bidang_sarana_prasarana' },
  { title: 'Bidang Humas', value: 'bidang_humas' },
]

const isFormValid = ref(false)
const isLoading = ref(false)
const refForm = ref<VForm>()
const fileFoto = ref<File[] | any>([])
const errors = ref()
const bidangs = ref([])
const panitiaData = ref<PanitiaData>(structuredClone(toRaw(props.panitiaData)))
const configs = ref({ dateFormat: 'Y-m-d', altFormat: 'd-m-Y', altInput: true, locale: Indonesian })

// const refInputEl = ref<HTMLElement>()

const fileRules = [requiredValidator,
  (fileList: FileList) => !fileList || !fileList.length || fileList[0].size < 5000000 || 'File maksimal 5 MB!']

watch(props, () => {
  panitiaData.value = structuredClone(toRaw(props.panitiaData))
})

const onFormReset = () => {
  panitiaData.value = structuredClone(toRaw(props.panitiaData))

  emit('update:isFormDialogVisible', false)
}

const dialogModelValueUpdate = (val: boolean) => {
  emit('update:isFormDialogVisible', val)
}

const defaultFoto = computed(() => {
  if (!panitiaData.value.avatar)
    return avatar1
  else
    return panitiaData.value.avatar
})

const onFormSubmit = async () => {
  try {
    isLoading.value = true

    const form = new FormData()

    form.append('nama', panitiaData.value.nama)
    form.append('jabatan', panitiaData.value.jabatan)
    form.append('lampiran_foto', fileFoto.value[0])
    form.append('bidang_id', panitiaData.value.bindangId.toString())
    form.append('is_status', props.isMusda ? 'musda' : 'muspanitra')

    const res = await $api('/panitia/create', {
      method: 'POST',
      body: form,
      onResponseError({ response }) {
        isLoading.value = false
        notyf.dismissAll()
        notyf.error(response._data.content[0].message!)
      },
    })

    isLoading.value = false
    notyf.dismissAll()
    notyf.success('Panitia berhasil ditambahkan')
    emit('update:isFormDialogVisible', false)

    emit('refetch')
    await nextTick(() => {
      refForm.value?.reset()
      refForm.value?.resetValidation()
    })
  }
  catch (err) {
    isLoading.value = false
    console.error(err)
  }
}

const onSubmit = () => {
  refForm.value?.validate()
    .then(({ valid: isValid }) => {
      if (isValid)
        onFormSubmit()
    })
}

const getBidang = async () => {
  try {
    const res = await $api('/bidang/get', {
      params: {
        is_status: props.isMusda ? 'musda' : 'muspanitra',
      },
      method: 'GET',
      onResponseError({ response }) {
        errors.value = response._data.errors
      },
    })

    bidangs.value = res.content

    await nextTick(() => {
      // router.replace(route.query.to ? String(route.query.to) : '/')
    })
  }
  catch (err) {
    console.error(err)
  }
}

// changeAvatar function
const changeAvatar = (file: Event) => {
  const fileReader = new FileReader()
  const { files } = file.target as HTMLInputElement

  if (files && files.length) {
    fileReader.readAsDataURL(files[0])
    fileReader.onload = () => {
      if (typeof fileReader.result === 'string')
        panitiaData.value.avatar = fileReader.result
    }
  }
}

const checkDigit = (event: KeyboardEvent) => {
  if (event.key.length === 1 && Number.isNaN(Number(event.key)))
    event.preventDefault()
}

const ageCalculate = computed(() => {
  if (panitiaData.value.tanggalLahir) {
    const birthdateDate = new Date(panitiaData.value.tanggalLahir)
    const now = new Date()
    const diff = now.getTime() - birthdateDate.getTime()

    return Math.floor(diff / (1000 * 60 * 60 * 24 * 365.25))
  }
  else {
    return 0
  }
})

watchEffect(() => {
  if (props.isFormDialogVisible) {
    if (fileFoto.value)
      panitiaData.value.avatar = ''

    getBidang()
  }
})
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isFormDialogVisible"
    persistent
    no-click-animation
    :retain-focus="false"
    @update:model-value="dialogModelValueUpdate"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="dialogModelValueUpdate(false)" />

    <VCard>
      <VCardText>
        <!-- 👉 Title -->
        <h4 class="text-h4 text-center">
          Formulir Panitia {{ isMusda ? 'Musda' : 'Muspanitra' }}
        </h4>

        <VDivider class="mb-4" />
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          v-model="isFormValid"

          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol
              cols="12"
              md="8"
            >
              <!-- 👉 Nama -->
              <VCol
                cols="12"
                md="8"
              >
                <AppTextField
                  v-model="panitiaData.nama"
                  label="Nama"
                  placeholder="John"
                  :rules="[requiredValidator]"
                />
              </VCol>

              <!-- 👉 Jenis Bidang -->
              <VCol
                cols="12"
                md="8"
              >
                <AppSelect
                  v-model="panitiaData.bindangId"
                  :items="bidangs"
                  label="Bidang Kepanitian"
                  placeholder="Pilih"
                  :rules="[requiredValidator]"
                />
              </VCol>

              <!-- 👉 Jenis Jabatan -->
              <VCol
                cols="12"
                md="8"
              >
                <AppSelect
                  v-model="panitiaData.jabatan"
                  label="Jabatan Kepanitian"
                  placeholder="Pilih"
                  :items="isMusda ? jabatans : jabatanMuspanitra"
                />
              </VCol>
            </VCol>
            <VCol
              cols="12"
              md="4"
              class="text-center"
            >
              <p class="text-body-1 text-center">
                Foto
              </p>

              <!-- 👉 Avatar -->
              <VAvatar
                rounded
                size="100"
                class="d-flex align-center justify-center mx-auto mt-2 mb-5 border rounded"
                :image="defaultFoto"
              />
              <VAlert
                color="primary"
                variant="outlined"
              >
                <span class="text-body-2 mb-2 text-center">
                  Hanya JPG, JPEG. Maksimal size 5MB. Pastikan foto tidak terbalik.
                </span>
              </VAlert>
              <!-- 👉 Upload Photo -->
              <div class="d-flex flex-column justify-center gap-5 mt-2">
                <VFileInput
                  v-model="fileFoto"
                  :rules="fileRules"
                  placeholder="Upload your documents"
                  label="Upload Foto"
                  prepend-icon="tabler-camera"
                  @input="changeAvatar"
                />
              </div>
            </VCol>
          </VRow>
          <!-- 👉 Submit and Cancel -->
          <VCol
            cols="12"
            class="d-flex flex-wrap justify-center gap-4 mt-5"
          >
            <VBtn
              type="submit"
              :loading="isLoading"
              prepend-icon="tabler-device-floppy"
            >
              Submit
            </VBtn>

            <VBtn
              color="secondary"
              variant="tonal"
              prepend-icon="tabler-square-rounded-x"
              @click="onFormReset"
            >
              Batal
            </VBtn>
          </VCol>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
