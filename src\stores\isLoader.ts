import { ref } from 'vue'
import { acceptHMRUpdate, defineStore } from 'pinia'

export const useIsLoader = defineStore('isLoader', () => {
  const isLoader = ref(false)

  function setIsLoader(value: boolean) {
    isLoader.value = value
  }

  return {
    isLoader,
    setIsLoader,
  } as const
})

/**
 * Pinia supports Hot Module replacement so you can edit your stores and
 * interact with them directly in your apps without reloading the page.
 *
 * @see https://pinia.esm.dev/cookbook/hot-module-replacement.html
 * @see https://vitejs.dev/guide/api-hmr.html
 */
if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useIsLoader, import.meta.hot))
