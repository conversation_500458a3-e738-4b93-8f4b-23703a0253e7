// 👉 Zoom fade
.app-transition-zoom-fade-enter-active,
.app-transition-zoom-fade-leave-active {
  transition: transform 0.35s, opacity 0.28s ease-in-out;
}

.app-transition-zoom-fade-enter-from {
  opacity: 0;
  transform: scale(0.98);
}

.app-transition-zoom-fade-leave-to {
  opacity: 0;
  transform: scale(1.02);
}

// 👉 Fade
.app-transition-fade-enter-active,
.app-transition-fade-leave-active {
  transition: opacity 0.25s ease-in-out;
}

.app-transition-fade-enter-from,
.app-transition-fade-leave-to {
  opacity: 0;
}

// 👉 Fade bottom
.app-transition-fade-bottom-enter-active,
.app-transition-fade-bottom-leave-active {
  transition: opacity 0.3s, transform 0.35s;
}

.app-transition-fade-bottom-enter-from {
  opacity: 0;
  transform: translateY(-0.6rem);
}

.app-transition-fade-bottom-leave-to {
  opacity: 0;
  transform: translateY(0.6rem);
}

// 👉 Slide fade
.app-transition-slide-fade-enter-active,
.app-transition-slide-fade-leave-active {
  transition: opacity 0.3s, transform 0.35s;
}

.app-transition-slide-fade-enter-from {
  opacity: 0;
  transform: translateX(-0.6rem);
}

.app-transition-slide-fade-leave-to {
  opacity: 0;
  transform: translateX(0.6rem);
}

// 👉 Zoom out
.app-transition-zoom-out-enter-active,
.app-transition-zoom-out-leave-active {
  transition: opacity 0.26s ease-in-out, transform 0.3s ease-out;
}

.app-transition-zoom-out-enter-from,
.app-transition-zoom-out-leave-to {
  opacity: 0;
  transform: scale(0.98);
}
