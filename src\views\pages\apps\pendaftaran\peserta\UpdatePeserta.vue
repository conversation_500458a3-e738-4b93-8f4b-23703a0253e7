<script setup lang="ts">
import { VForm } from 'vuetify/components/VForm'

import { Indonesian } from 'flatpickr/dist/l10n/id'
import avatar1 from '@images/avatars/default-avatar.png'
import { useNotyf } from '@/composables/useNotyf'

const props = withDefaults(defineProps<Props>(), {
  isEditFormDialogVisible: undefined,
  userData: () => ({
    id: '',
    nama: '',
    email: '',
    alamat: '',
    role: '',
    no_hp: '',
    umur: '',
    jabatan_id: '',
    jabatan: '',
    avatar: '',
    lampiran_foto: '',
    lampiran_kta: '',
    tgl_lahir: '',
    jenis_kelamin: '',
  }),
})

const emit = defineEmits<Emit>()

const notyf = useNotyf()

interface UserData {
  id: string
  nama: string
  email: string
  alamat: string
  no_hp: string
  role: string
  umur: number
  jabatan_id: string
  jabatan: string
  avatar: string
  lampiran_foto: string
  lampiran_kta: string
  tgl_lahir: string
  jenis_kelamin: string
}

interface Props {
  userData?: UserData
  isEditFormDialogVisible: boolean
}

interface Emit {
  (e: 'refetch'): void
  (e: 'update:isEditFormDialogVisible', val: boolean): void
}

const baseURL = import.meta.env.VITE_API_BASE_URL
const isFormValid = ref(false)
const isLoading = ref(false)
const refForm = ref<VForm>()
const fileKta = ref<File>()
const fileFoto = ref<File>()
const errors = ref()
const jabatans = ref([])
const userData = ref<UserData>(structuredClone(toRaw(props.userData)))
const configs = ref({ dateFormat: 'Y-m-d', altFormat: 'd-m-Y', altInput: true, locale: Indonesian })

// const refInputEl = ref<HTMLElement>()

const fileRules = [
  (fileList: FileList) => !fileList || !fileList.length || fileList[0].size < 5000000 || 'File maksimal 5 MB!',
]

watch(props, () => {
  userData.value = structuredClone(toRaw(props.userData))
})

const onFormReset = () => {
  userData.value = structuredClone(toRaw(props.userData))

  emit('update:isEditFormDialogVisible', false)
}

const dialogModelValueUpdate = (val: boolean) => {
  emit('update:isEditFormDialogVisible', val)
}

const defaultFoto = computed(() => {
  if (!userData.value.lampiran_foto)
    return avatar1
  else if (userData.value.avatar)
    return userData.value.avatar
  else
    return `${baseURL}/storage/uploads/photos/peserta/${userData.value.lampiran_foto}`
})

const getJabatan = async () => {
  try {
    const res = await $api('/jabatan/get', {
      method: 'GET',
      onResponseError({ response }) {
        errors.value = response._data.errors
      },
    })

    jabatans.value = res.content

    await nextTick(() => {
      // router.replace(route.query.to ? String(route.query.to) : '/')
    })
  }
  catch (err) {
    console.error(err)
  }
}

const onFormSubmit = async () => {
  try {
    isLoading.value = true

    const form = new FormData()

    form.append('nama', userData.value.nama)
    form.append('email', userData.value.email)
    form.append('no_hp', userData.value.no_hp)
    form.append('alamat', userData.value.alamat)
    form.append('tgl_lahir', userData.value.tgl_lahir)
    form.append('umur', ageCalculate.value)
    form.append('jabatan_id', userData.value.jabatan_id)
    form.append('jenis_kelamin', userData.value.jenis_kelamin)
    if (fileFoto.value)
      form.append('lampiran_foto', fileFoto.value[0])

    if (fileKta.value)
      form.append('lampiran_kta', fileKta.value[0])

    const res = await $api(`/peserta/update/${userData.value.id}`, {
      method: 'POST',
      body: form,
      onResponseError({ response }) {
        isLoading.value = false
        notyf.dismissAll()
        notyf.error(response._data.content[0].message!)
      },
    })

    isLoading.value = false
    notyf.dismissAll()
    notyf.success('Peserta berhasil diupdate')
    emit('update:isEditFormDialogVisible', false)

    emit('refetch')
    await nextTick(() => {
      refForm.value?.reset()
      refForm.value?.resetValidation()
    })
  }
  catch (err) {
    isLoading.value = false
    console.error(err)
  }
}

const onSubmit = () => {
  refForm.value?.validate()
    .then(({ valid: isValid }) => {
      if (isValid)
        onFormSubmit()
    })
}

// changeAvatar function
const changeAvatar = (file: Event) => {
  const fileReader = new FileReader()
  const { files } = file.target as HTMLInputElement

  if (files && files.length) {
    fileReader.readAsDataURL(files[0])
    fileReader.onload = () => {
      if (typeof fileReader.result === 'string')
        userData.value.avatar = fileReader.result
    }
  }
}

const checkDigit = (event: KeyboardEvent) => {
  if (event.key.length === 1 && Number.isNaN(Number(event.key)))
    event.preventDefault()
}

const ageCalculate = computed(() => {
  if (userData.value.tgl_lahir) {
    const birthdateDate = new Date(userData.value.tgl_lahir)
    const now = new Date()
    const diff = now.getTime() - birthdateDate.getTime()

    return Math.floor(diff / (1000 * 60 * 60 * 24 * 365.25))
  }
  else {
    return 0
  }
})

watchEffect(() => {
  // eslint-disable-next-line sonarjs/no-collapsible-if
  if (props.isEditFormDialogVisible) {
    if (fileFoto.value)
      userData.value.avatar = ''
  }
})
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 1000"
    :model-value="props.isEditFormDialogVisible"
    persistent
    no-click-animation
    :retain-focus="false"
    @update:model-value="dialogModelValueUpdate"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="dialogModelValueUpdate(false)" />

    <VCard>
      <VCardText>
        <!-- 👉 Title -->
        <h4 class="text-h4 text-center">
          Formulir Edit Peserta
        </h4>
        <p class="text-body-1 text-center">
          {{ props.userData.jabatan }}
        </p>
        <VDivider class="mb-4" />
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          v-model="isFormValid"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol
              cols="12"
              md="8"
            >
              <VRow>
                <!-- 👉 Nama -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="userData.nama"
                    label="Nama"
                    placeholder="John"
                    :rules="[requiredValidator]"
                  />
                </VCol>

                <!-- 👉 Email -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="userData.email"
                    label="Email"
                    placeholder="<EMAIL>"
                    :rules="[requiredValidator, emailValidator]"
                  />
                </VCol>

                <!-- 👉 No.HP -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="userData.no_hp"
                    label="No.HP"
                    placeholder="0852xxxxxxxx"
                    :rules="[requiredValidator]"
                    @keydown="checkDigit"
                  />
                </VCol>

                <!-- 👉 Alamat -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="userData.alamat"
                    label="Alamat"
                    placeholder="Jl..."
                    :rules="[requiredValidator]"
                  />
                </VCol>

                <!-- 👉 Tanggal lahir -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppDateTimePicker
                    v-model="userData.tgl_lahir"
                    label="Tanggal Lahir"
                    placeholder=""
                    :rules="[requiredValidator]"
                    :config="configs"
                  />
                </VCol>

                <!-- 👉 Umur -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="ageCalculate"
                    label="Umur"
                    placeholder="0"
                    readonly
                  />
                </VCol>

                <!-- 👉 Jenis Kelamin -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppSelect
                    v-model="userData.jenis_kelamin"
                    label="Jenis Kelamin"
                    placeholder="Laki-Laki"
                    :items="['Laki-Laki', 'Perempuan']"
                    :rules="[requiredValidator]"
                  />
                </VCol>

                <!-- 👉 Jabatan -->
                <VCol
                  cols="12"
                  md="6"
                  class="mt-6"
                >
                  <VFileInput
                    v-model="fileKta"
                    :rules="fileRules"
                    placeholder="Upload your documents"
                    label="Upload KTA"
                    prepend-icon="
                  tabler-paperclip"
                    accept=".jpg, .jpeg, .pdf"
                  >
                    <template #selection="{ fileNames }">
                      <template
                        v-for="fileName in fileNames"
                        :key="fileName"
                      >
                        <VChip
                          label
                          size="small"
                          color="primary"
                          class="me-2"
                        >
                          {{ fileName }}
                        </VChip>
                      </template>
                    </template>
                  </VFileInput>
                </VCol>
              </VRow>
            </VCol>
            <VCol
              cols="12"
              md="4"
              class="text-center"
            >
              <p class="text-body-1 text-center">
                Foto
              </p>

              <!-- 👉 Avatar -->
              <VAvatar
                rounded
                size="100"
                class="d-flex align-center justify-center mx-auto mt-2 mb-5 border rounded"
                :image="defaultFoto"
              />
              <VAlert
                color="primary"
                variant="outlined"
                class="gap-2"
              >
                <span class="text-body-2 mb-2 text-center">
                  Hanya JPG, JPEG. Maksimal size 5MB. Pastikan foto tidak terbalik.
                </span>
              </VAlert>
              <!-- 👉 Upload Photo -->
              <div class="d-flex flex-column justify-center gap-5 mt-2">
                <VFileInput
                  v-model="fileFoto"
                  :rules="fileRules"
                  placeholder="Upload your documents"
                  label="Upload Foto"
                  prepend-icon="tabler-camera"
                  accept=".jpg, .jpeg"
                  @input="changeAvatar"
                />
              </div>
            </VCol>
          </VRow>
          <!-- 👉 Submit and Cancel -->
          <VCol
            cols="12"
            class="d-flex flex-wrap justify-center gap-4 mt-5"
          >
            <VBtn
              type="submit"
              :loading="isLoading"
              prepend-icon="tabler-device-floppy"
            >
              Update
            </VBtn>

            <VBtn
              color="secondary"
              variant="tonal"
              prepend-icon="tabler-square-rounded-x"
              @click="onFormReset"
            >
              Batal
            </VBtn>
          </VCol>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
