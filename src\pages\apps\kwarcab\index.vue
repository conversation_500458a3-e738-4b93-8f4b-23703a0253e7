<script setup lang="ts">
import type { UserProperties } from '@db/apps/kwarcabs/types'
import { useHead } from '@unhead/vue'
import { themeConfig } from '@themeConfig'

// 👉 Store
const searchQuery = ref('')
const selectedStatus = ref()
const errors = ref()
const isLoadingMusda = ref(false)
const isLoadingMuspanitra = ref(false)
const isLoadingMandat = ref(false)
const isConfirmDialogVisible = ref(false)
const selectedData = ref()

// Data table options
const limit = ref(10)
const offset = ref(1)
const sortBy = ref()
const orderBy = ref()

// Update data table options
const updateOptions = (options: any) => {
  sortBy.value = options.sortBy[0]?.key
  orderBy.value = options.sortBy[0]?.order
}

const headers = [
  { title: '#', key: 'index' },
  { title: 'Kwarcab', key: 'nama', width: 400 },
  { title: 'Musda', key: 'musda', width: 400 },
  { title: 'Muspanitra', key: 'muspanitra', width: 400 },
  { title: 'ID Card Musda', key: 'id_card_musda', width: 400, align: 'center' },
  { title: 'ID Card Muspanitra', key: 'id_card_muspan', width: 400, align: 'center' },
  { title: 'Lampiran Mandat', key: 'lampiran_mandat', width: 400, align: 'center' },
]

// 👉 Fetching kwarcabs
const { data: panitiaData, execute: fetchData } = await useApi<any>(createUrl('/pendaftaran/list-kwarcab', {
  query: {
    q: searchQuery,
    status: selectedStatus,
    limit,
    offset,
    sortBy,
    orderBy,
  },
}))

const kwarcabs = computed((): UserProperties[] => panitiaData.value.content!.kwarcabs)
const totalData = computed(() => panitiaData.value.content.total)

// 👉 Delete data
const deleteData = async (id: number) => {
  await $api(`/apps/kwarcabs/${id}`, {
    method: 'DELETE',
  })

  // refetch User
  // TODO: Make this async
  await fetchData()
}

useHead({
  title: `List Kwarcab - ${themeConfig.app.title}`,
})
definePage({
  meta: {
    action: 'List User',
    subject: 'peserta.list',
  },
})

const downloadBadgeMusda = async (row: any) => {
  selectedData.value = row
  isLoadingMusda.value = true

  try {
    const res: Blob = await $api('/badge/musda-download', {
      method: 'GET',
      responseType: 'blob',
      params: {
        kwarcab_id: row.id,
      },

      onResponseError({ response }) {
        isLoadingMusda.value = false
        selectedData.value = null
        errors.value = response._data.errors
      },
    })

    isLoadingMusda.value = false
    selectedData.value = null

    const fileURL = window.URL.createObjectURL(new Blob([res]))
    const fileLink = document.createElement('a')

    fileLink.href = fileURL

    fileLink.setAttribute(
      'download',
      `ID-CARD-MUSDA-KWARCAB-${row.name}.zip`,
    )
    document.body.appendChild(fileLink)
    fileLink.click()
  }
  catch (err) {
    isLoadingMusda.value = false
    selectedData.value = null
    console.error(err)
  }
}

const downloadBadgeMuspanitra = async (row: any) => {
  selectedData.value = row
  isLoadingMuspanitra.value = true

  try {
    const res: Blob = await $api('/badge/muspanitra-download', {
      method: 'GET',
      responseType: 'blob',
      params: {
        kwarcab_id: row.id,
      },

      onResponseError({ response }) {
        isLoadingMuspanitra.value = false
        selectedData.value = null
        errors.value = response._data.errors
      },
    })

    isLoadingMuspanitra.value = false
    selectedData.value = null

    const fileURL = window.URL.createObjectURL(new Blob([res]))
    const fileLink = document.createElement('a')

    fileLink.href = fileURL

    fileLink.setAttribute(
      'download',
      `ID-CARD-MUSPANITRA-KWARCAB-${row.name}.zip`,
    )
    document.body.appendChild(fileLink)
    fileLink.click()
  }
  catch (err) {
    isLoadingMuspanitra.value = false
    selectedData.value = null
    console.error(err)
  }
}

const downloadMandatZip = async (row: any) => {
  selectedData.value = row
  isLoadingMandat.value = true

  try {
    const res: Blob = await $api('/pendaftaran/download-lampiran-mandat', {
      method: 'GET',
      responseType: 'blob',
      params: {
        kwarcab_id: row.id,
      },

      onResponseError({ response }) {
        isLoadingMandat.value = false
        selectedData.value = null
        errors.value = response._data.errors
      },
    })

    isLoadingMandat.value = false
    selectedData.value = null

    const fileURL = window.URL.createObjectURL(new Blob([res]))
    const fileLink = document.createElement('a')

    fileLink.href = fileURL

    fileLink.setAttribute(
      'download',
      `LAMPIRAN-MANDAT-KWARCAB-${row.name}.zip`,
    )
    document.body.appendChild(fileLink)
    fileLink.click()
  }
  catch (err) {
    isLoadingMandat.value = false
    selectedData.value = null
    console.error(err)
  }
}

onMounted(() => {

})
</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardText class="d-flex flex-wrap gap-4">
        <VSpacer />

        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <!-- 👉 Search  -->
          <div style="inline-size: 15.625rem;">
            <AppTextField
              v-model="searchQuery"
              placeholder="Cari..."
            />
          </div>
        </div>
      </VCardText>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer
        v-model:items-per-page="limit"
        v-model:page="offset"
        :items="kwarcabs"
        :items-length="totalData"
        :headers="headers"
        class="text-no-wrap"
        show-select
        @update:options="updateOptions"
      >
        <!-- index -->
        <template #item.index="{ index }">
          <div class="d-flex align-center">
            <div class="d-flex flex-column">
              {{ Math.ceil(offset * limit - limit + index + 1) }}
            </div>
          </div>
        </template>

        <!-- nama -->
        <template #item.nama="{ item }">
          <div class="text-body-1 text-high-emphasis text-capitalize">
            <div class="me-4">
              {{ item.name }}
            </div>
          </div>
        </template>

        <!-- musda -->
        <template #item.musda="{ item }">
          <div class="text-body-1 text-high-emphasis text-capitalize">
            <div class="me-4">
              {{ item.peserta_musda }} Orang
            </div>
          </div>
        </template>
        <!-- muspanitra -->
        <template #item.muspanitra="{ item }">
          <div class="text-body-1 text-high-emphasis text-capitalize">
            <div class="me-4">
              {{ item.peserta_muspanitra }} Orang
            </div>
          </div>
        </template>
        <!-- id_card -->
        <template #item.id_card_musda="{ item }">
          <div class="text-center">
            <!-- 👉 Download button -->
            <VBtn
              :disabled="item.peserta_musda === 0"
              :loading="isLoadingMusda && selectedData.id === item.id"
              size="x-small"
              color="success"
              prepend-icon="tabler-download"
              @click.prevent="downloadBadgeMusda(item)"
            >
              Download
            </VBtn>
          </div>
        </template>
        <!-- id_card -->
        <template #item.id_card_muspan="{ item }">
          <div class="text-center">
            <!-- 👉 Download button -->
            <VBtn
              :disabled="item.peserta_muspanitra === 0"
              :loading="isLoadingMuspanitra && selectedData.id === item.id"
              size="x-small"
              color="warning"
              prepend-icon="tabler-download"
              @click.prevent="downloadBadgeMuspanitra(item)"
            >
              Download
            </VBtn>
          </div>
        </template>

        <!-- lampiran mandat -->
        <template #item.lampiran_mandat="{ item }">
          <div class="text-center">
            <!-- 👉 Download button -->
            <VBtn
             :disabled="item.peserta_musda === 0"
              :loading="isLoadingMandat && selectedData.id === item.id"
              size="x-small"
              color="info"
              prepend-icon="tabler-download"
              @click.prevent="downloadMandatZip(item)"
            >
              Download
            </VBtn>
          </div>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination
            v-model:page="offset"
            :items-per-page="limit"
            :total-items="totalData"
          />
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
    <!-- 👉 Confirm Dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isConfirmDialogVisible"
      cancel-title="Batal"
      confirm-title="Berhasil!"
      confirm-msg="Panitia berhasil dihapus!."
      confirmation-question="Yakin ingin hapus Panitia ini ?"
      cancel-msg="Batal hapus Panitia!"
      @submit="deleteData"
    />
  </section>
</template>
