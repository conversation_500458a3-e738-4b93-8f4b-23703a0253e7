<script setup lang="ts">
import type { UserProperties } from '@db/apps/users/types'
import { useHead } from '@unhead/vue'
import { themeConfig } from '@themeConfig'

import AddNewPanitia from '@/views/pages/apps/panitia/AddNewPanitia.vue'
import UpdatePanitia from '@/views/pages/apps/panitia/UpdatePanitia.vue'
import DetailPanitia from '@/views/pages/apps/panitia/DetailPanitia.vue'
import avatar1 from '@images/avatars/default-avatar.png'

// 👉 Store
const baseURL = import.meta.env.VITE_API_BASE_URL
const searchQuery = ref('')
const selectedStatus = ref()
const errors = ref()
const isLoading = ref(false)
const isConfirmDialogVisible = ref(false)
const selectedData = ref()
const isDetailFormDialogVisible = ref(false)
const isFormDialogVisible = ref(false)
const isEditFormDialogVisible = ref(false)
const isMusda = ref(true)

// Data table options
const itemsPerPage = ref(10)
const page = ref(1)
const sortBy = ref()
const orderBy = ref()
const bidangId = ref()

interface Bidang {
  id?: string
  code?: string
  name: string
}

const bidangs = ref<Bidang>()

// Update data table options
const updateOptions = (options: any) => {
  sortBy.value = options.sortBy[0]?.key
  orderBy.value = options.sortBy[0]?.order
}

const headers = [
  { title: '#', key: 'index' },
  { title: 'Foto', key: 'foto', width: 400 },
  { title: 'Nama', key: 'nama', width: 400 },
  { title: 'Bidang', key: 'bidang', width: 400 },
  { title: 'Jabatan', key: 'jabatan', width: 400 },
  { title: 'Aksi', key: 'actions', sortable: false, align: 'center' },
]

// 👉 Fetching users
const { data: panitiaData, execute: fetchData } = await useApi<any>(createUrl('/panitia/list', {
  query: {
    q: searchQuery,
    status: selectedStatus,
    limit: itemsPerPage,
    offset: page,
    sortBy,
    orderBy,
    bidang_id: bidangId,
    is_status: isMusda.value ? 'musda' : 'muspanitra',
  },
}))

const users = computed((): UserProperties[] => panitiaData.value.content!.panitias)
const totalData = computed(() => panitiaData.value.content.total)

const resolveUserStatusVariant = (stat: number) => {
  const statLowerCase = stat
  if (statLowerCase === 1) {
    return {
      variant: 'primary',
      label: 'Ketua/ Ketua Harian',
    }
  }

  if (statLowerCase === 2) {
    return {
      variant: 'success',
      label: 'Sekretaris',
    }
  }
  if (statLowerCase === 3) {
    return {
      variant: 'info',
      label: 'Waka Orgakum/ Waka lainnya',
    }
  }
  if (statLowerCase === 4) {
    return {
      variant: 'warning',
      label: 'Kapusdiklatcab',
    }
  }

  return {
    variant: 'danger',
    label: 'Ketua DKC',
  }
}

const resolveAvatar = (foto: string) => {
  if (foto !== '')
    return `${baseURL}/storage/uploads/photos/panitia/${foto}`

  return avatar1
}

const isAddNewDivisiDrawerVisible = ref(false)

// 👉 Update new Divisi

const getBidang = async () => {
  try {
    const res = await $api('/bidang/get', {
      method: 'GET',
      onResponseError({ response }) {
        errors.value = response._data.errors
      },
    })

    bidangs.value = res.content

    await nextTick(() => {
      // router.replace(route.query.to ? String(route.query.to) : '/')
    })
  }
  catch (err) {
    console.error(err)
  }
}

// 👉 Delete data
const deleteData = async (id: number) => {
  await $api(`/apps/users/${id}`, {
    method: 'DELETE',
  })

  // refetch User
  // TODO: Make this async
  await fetchData()
}

const openDetailDataSelected = (row: any) => {
  selectedData.value = row
  isDetailFormDialogVisible.value = true
}

const openDeleteDataSelected = (row: any) => {
  selectedData.value = row
  isConfirmDialogVisible.value = true
}

const openEditDataSelected = (row: any) => {
  selectedData.value = row
  isEditFormDialogVisible.value = true
}

const openAddForm = (row: any) => {
  isFormDialogVisible.value = true
}

useHead({
  title: `Panitia Musda - ${themeConfig.app.title}`,
})
definePage({
  meta: {
    action: 'List User',
    subject: 'peserta.list',
  },
})

const downloadFile = async () => {
  isLoading.value = true

  try {
    const res: Blob = await $api('/badge/panitia-musda-download', {
      method: 'GET',
      responseType: 'blob',

      onResponseError({ response }) {
        isLoading.value = false

        errors.value = response._data.errors
      },
    })

    isLoading.value = false

    const fileURL = window.URL.createObjectURL(new Blob([res]))
    const fileLink = document.createElement('a')

    fileLink.href = fileURL

    fileLink.setAttribute(
      'download',
      'ID-CARD-PANITIA-MUSDA.zip',
    )
    document.body.appendChild(fileLink)
    fileLink.click()
  }
  catch (err) {
    isLoading.value = false
    console.error(err)
  }
}

const downloadIdCard = async (row: any) => {

  try {
    const res: Blob = await $api(`/badge/download/idcard/panitia-musda/${row.id}`, {
      method: 'GET',
      responseType: 'blob',

      onResponseError({ response }) {

        errors.value = response._data.errors
      },
    })

    const fileURL = window.URL.createObjectURL(new Blob([res]))
    const fileLink = document.createElement('a')

    fileLink.href = fileURL

    fileLink.setAttribute(
      'download',
      `ID-CARD-PANITIA-MUSDA-${row.nomor_panitia}.png`,
    )
    document.body.appendChild(fileLink)
    fileLink.click()
  }
  catch (err) {
    console.error(err)
  }
}

onMounted(() => {
  getBidang()
})
</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardText class="d-flex flex-wrap gap-4">
        <div class="me-3 d-flex gap-3">
          <AppSelect v-model="bidangId" :items="bidangs" tem-title="title" item-value="value" clearable
            clear-icon="tabler-x" style="inline-size: 16.25rem;" placeholder="Bidang" />
        </div>
        <VSpacer />

        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <!-- 👉 Search  -->
          <div style="inline-size: 15.625rem;">
            <AppTextField v-model="searchQuery" placeholder="Cari..." />
          </div>

          <!-- 👉 Add user button -->
          <VBtn prepend-icon="tabler-plus" @click="openAddForm">
            Baru
          </VBtn>
          <!-- 👉 Download button -->
          <VBtn :loading="isLoading" color="success" prepend-icon="tabler-download" :disabled="totalData === 0"
            @click.prevent="downloadFile">
            ID Card
          </VBtn>
        </div>
      </VCardText>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer v-model:items-per-page="itemsPerPage" v-model:page="page" :items="users"
        :items-length="totalData" :headers="headers" class="text-no-wrap" show-select @update:options="updateOptions">
        <!-- index -->
        <template #item.index="{ index }">
          <div class="d-flex align-center">
            <div class="d-flex flex-column">
              {{ Math.ceil(page * itemsPerPage - itemsPerPage + index + 1) }}
            </div>
          </div>
        </template>

        <!-- foto -->
        <template #item.foto="{ item }">
          <div class="text-body-1 text-high-emphasis text-capitalize">
            <div class="me-4">
              <VAvatar :image="resolveAvatar(item.lampiran_foto)" width="50" />
            </div>
          </div>
        </template>

        <!-- nama -->
        <template #item.nama="{ item }">
          <div class="text-body-1 text-high-emphasis text-capitalize">
            <div class="me-4">
              {{ item.nama }}
            </div>
          </div>
        </template>
        <!-- bidang -->
        <template #item.bidang="{ item }">
          <div class="text-body-1 text-high-emphasis text-capitalize">
            {{ item.bidang }}
          </div>
        </template>

        <!-- Status -->
        <template #item.jabatan="{ item }">
          <VChip :color="resolveUserStatusVariant(item.jabatan_id).variant" size="small" label class="text-capitalize">
            {{ item.jabatan }}
          </VChip>
        </template>

        <!-- Actions -->
        <template #item.actions="{ item }">
          <IconBtn @click="downloadIdCard(item)">
            <VIcon icon="tabler-id-badge-2" />
          </IconBtn>
          <IconBtn @click="openEditDataSelected(item)">
            <VIcon icon="tabler-pencil" />
          </IconBtn>

          <IconBtn @click="openDetailDataSelected(item)">
            <VIcon icon="tabler-eye" />
          </IconBtn>
          <IconBtn @click="openDeleteDataSelected(item)">
            <VIcon icon="tabler-trash" />
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination v-model:page="page" :items-per-page="itemsPerPage" :total-items="totalData" />
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>

    <AddNewPanitia v-model:isFormDialogVisible="isFormDialogVisible" :jabatan-selected="selectedData"
      :is-musda="isMusda" @refetch="fetchData" />
    <UpdatePanitia v-model:isEditFormDialogVisible="isEditFormDialogVisible" :panitia-data="selectedData"
      :is-musda="isMusda" @refetch="fetchData" />
    <DetailPanitia v-model:isDetailFormDialogVisible="isDetailFormDialogVisible" :panitia-data="selectedData" />
    <!-- 👉 Confirm Dialog -->
    <ConfirmDialog v-model:isDialogVisible="isConfirmDialogVisible" cancel-title="Batal" confirm-title="Berhasil!"
      confirm-msg="Panitia berhasil dihapus!." confirmation-question="Yakin ingin hapus Panitia ini ?"
      cancel-msg="Batal hapus Panitia!" @submit="deleteData" />
  </section>
</template>
