import { acceptHMRUpdate, defineStore } from 'pinia'
import { ref } from 'vue'
import { useNotyf } from '@/composables/useNotyf'

interface Pendaftaran {
  id: string
  kwarcab_id: number
  lampiran_mandat: string
  is_status: string
  size: number
  extension: string
  is_musda: number

}

// const errors = ref<any> ()
export const usePendaftaran = defineStore('pendaftaran', () => {
  const pendaftaran = ref<[Pendaftaran]>()
  const dataPeserta = ref<[]>([])
  const notyf = useNotyf()
  async function getPendaftaran() {
    try {
      const res = await $api('/pendaftaran/get', {
        method: 'GET',

        onResponseError({ response }) {
          notyf.dismissAll()
          notyf.error(response._data.content[0].message)
        },

      })

      pendaftaran.value = res.content
    }
    catch (err) {
      console.error(err)
    }
  }

  async function getDataPeserta() {
    try {
      const res = await $api('/peserta/list', {
        params: {
          pendaftaran: true,

        },
        method: 'GET',

        onResponseError({ response }) {
          notyf.dismissAll()
          notyf.error(response._data.content[0].message!)
        },

      })

      dataPeserta.value = res.content!.pesertas
    }
    catch (err) {
      console.error(err)
    }
  }

  return {
    pendaftaran,
    getPendaftaran,
    dataPeserta,
    getDataPeserta,

  } as const
})

/**
 * Pinia supports Hot Module replacement so you can edit your stores and
 * interact with them directly in your apps without reloading the page.
 *
 * @see https://pinia.esm.dev/cookbook/hot-module-replacement.html
 * @see https://vitejs.dev/guide/api-hmr.html
 */
if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(usePendaftaran, import.meta.hot))
