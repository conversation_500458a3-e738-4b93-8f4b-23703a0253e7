import { createHead } from '@unhead/vue'
import { createApp } from 'vue'
import App from '@/App.vue'
import { notyf, notyfSymbol } from '@/plugins/notyf'
import { registerPlugins } from '@core/utils/plugins'

// Styles
import '@core/scss/template/index.scss'
import '@styles/styles.scss'
import 'notyf/notyf.min.css'

// Create vue app
const app = createApp(App)

notyf.then(resolvedNotyf => {
  app.provide(notyfSymbol, resolvedNotyf)
})

// Register plugins
registerPlugins(app)

const head = createHead()

app.use(head)

// Mount vue app
app.mount('#app')
