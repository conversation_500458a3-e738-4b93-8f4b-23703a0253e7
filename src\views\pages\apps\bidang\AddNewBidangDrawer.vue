<script setup lang="ts">
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
// eslint-disable-next-line @typescript-eslint/consistent-type-imports
import type { VForm } from 'vuetify/components/VForm'

interface Emit {
  (e: 'update:isDrawerOpen', value: boolean): void
  (e: 'refetch'): void
}

interface Bidang {
  id: string
  nama_bidang: string
  is_status: string
}

interface Props {
  isDrawerOpen: boolean
  dataSelected: Bidang | Blob
}

const props = defineProps<Props>()
const emit = defineEmits<Emit>()

const isFormValid = ref(false)
const refForm = ref<VForm>()
const namaBidang = ref('')
const isStatus = ref()

// 👉 drawer close
const closeNavigationDrawer = () => {
  emit('update:isDrawerOpen', false)

  nextTick(() => {
    refForm.value?.reset()
    refForm.value?.resetValidation()
  })
}

const onSubmit = () => {
  refForm.value?.validate().then(({ valid }) => {
    if (valid) {
      if (props.dataSelected) {
        emit('form-update', {
          id: props.dataSelected?.id,
          namaBidang: namaBidang.value,
          isStatus: isStatus.value,
        })
      }
      else {
        emit('refetch', {
          namaBidang: namaBidang.value,
          isStatus: isStatus.value,
        })
      }

      emit('update:isDrawerOpen', false)
      nextTick(() => {
        refForm.value?.reset()
        refForm.value?.resetValidation()
      })
    }
  })
}

const handleDrawerModelValueUpdate = (val: boolean) => {
  emit('update:isDrawerOpen', val)
}

watchEffect(() => {
  if (props.isDrawerOpen) {
    if (props.dataSelected) {
      namaBidang.value = props.dataSelected.nama_bidang
      isStatus.value = props.dataSelected.is_status
    }
  }
  else {
    nextTick(() => {
      refForm.value?.reset()
      refForm.value?.resetValidation()
    })
  }
})
</script>

<template>
  <VNavigationDrawer
    temporary
    :width="400"
    location="end"
    class="scrollable-content"
    :model-value="props.isDrawerOpen"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Title -->
    <AppDrawerHeaderSection
      :title="props.dataSelected ? 'Edit Bidang' : 'Tambah Bidang'"
      @cancel="closeNavigationDrawer"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <!-- 👉 Form -->
          <VForm
            ref="refForm"
            v-model="isFormValid"
            @submit.prevent="onSubmit"
          >
            <VRow>
              <!-- 👉 Full name -->
              <VCol cols="12">
                <AppTextField
                  v-model="namaBidang"
                  :rules="[requiredValidator]"
                  label="Nama Bidang"
                  placeholder="Ex: Administrasi"
                />
              </VCol>

              <!-- 👉 Status -->
              <VCol cols="12">
                <AppSelect
                  v-model="isStatus"
                  label="Status"
                  placeholder="Pilih"
                  :rules="[requiredValidator]"
                  :items="[{ title: 'Aktif', value: 'active' }, { title: 'Tidak Aktif', value: 'inactive' }]"
                />
              </VCol>

              <!-- 👉 Submit and Cancel -->
              <VCol cols="12">
                <VBtn
                  type="submit"
                  class="me-3"
                >
                  {{ props.dataSelected ? 'Update' : 'Submit' }}
                </VBtn>
                <VBtn
                  type="reset"
                  variant="tonal"
                  color="error"
                  @click="closeNavigationDrawer"
                >
                  Batal
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>
