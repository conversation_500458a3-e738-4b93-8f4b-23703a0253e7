<script setup lang="ts">
import { useConfigSystem } from '@/stores/configSystem'

const configSystem = useConfigSystem()

const textStatusPendaftaran = computed(() => {
  if (configSystem.configSystem?.status_pendaftaran)
    return 'Pendaftaran telah ditutup. Terimakasih'
  else
    return `Data pendaftaran dapat diubah sebelum tanggal ${configSystem.configSystem?.tanggal_tutup}.`
})
</script>

<template>
  <VCard class="mb-0">
    <VCardText class="position-relative">
      <div class="d-flex flex-column gap-y-4 mx-auto">
        <VAlert
          :type="configSystem.configSystem?.status_pendaftaran ? 'success' : 'info'"
          variant="tonal"
          class="mt-4"
        >
          {{ textStatusPendaftaran }}
        </VAlert>
        <div class="d-flex justify-center align-center gap-4 flex-wrap">
          <div
            class="flex-grow-1"
            style="max-inline-size: 350px;"
          />
        </div>
      </div>
    </VCardText>
  </VCard>
</template>

<style scoped lang="scss">

</style>
