{"name": "vuexy-vuejs-admin-template", "version": "9.1.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 5050", "typecheck": "vue-tsc --noEmit", "lint": "eslint . -c .eslintrc.cjs --fix --ext .ts,.js,.cjs,.vue,.tsx,.jsx", "build:icons": "tsx src/plugins/iconify/build-icons.ts", "msw:init": "msw init public/ --save", "postinstall": "npm run build:icons && npm run msw:init"}, "dependencies": {"@casl/ability": "6.7.0", "@casl/vue": "2.2.2", "@floating-ui/dom": "1.6.1", "@iconify-json/fa": "1.1.8", "@iconify-json/tabler": "1.1.106", "@sindresorhus/is": "6.2.0", "@tiptap/extension-highlight": "2.2.4", "@tiptap/extension-image": "2.2.4", "@tiptap/extension-link": "2.2.4", "@tiptap/extension-text-align": "2.2.4", "@tiptap/pm": "2.2.4", "@tiptap/starter-kit": "2.2.4", "@tiptap/vue-3": "2.2.4", "@unhead/vue": "^1.9.7", "@vueuse/core": "10.9.0", "@vueuse/math": "10.9.0", "apexcharts": "3.46.0", "chart.js": "4.4.2", "cookie-es": "1.0.0", "eslint-plugin-regexp": "2.2.0", "jwt-decode": "4.0.0", "mapbox-gl": "3.1.2", "notyf": "^3.10.0", "ofetch": "1.3.3", "pinia": "2.1.7", "prismjs": "1.29.0", "roboto-fontface": "0.10.0", "shepherd.js": "11.2.0", "swiper": "11.0.7", "ufo": "1.4.0", "unplugin-vue-define-options": "1.4.2", "vue": "3.4.21", "vue-chartjs": "5.3.0", "vue-flatpickr-component": "11.0.3", "vue-i18n": "9.10.1", "vue-prism-component": "2.0.0", "vue-router": "4.3.0", "vue3-apexcharts": "1.5.2", "vue3-markdown": "^1.1.9", "vue3-perfect-scrollbar": "1.6.1", "vuetify": "3.5.2", "webfontloader": "1.6.28"}, "devDependencies": {"@antfu/eslint-config-vue": "0.43.1", "@antfu/utils": "0.7.7", "@fullcalendar/core": "6.1.11", "@fullcalendar/daygrid": "6.1.11", "@fullcalendar/interaction": "6.1.11", "@fullcalendar/list": "6.1.11", "@fullcalendar/timegrid": "6.1.11", "@fullcalendar/vue3": "6.1.11", "@iconify-json/mdi": "1.1.64", "@iconify/tools": "4.0.2", "@iconify/utils": "2.1.22", "@iconify/vue": "4.1.1", "@intlify/unplugin-vue-i18n": "2.0.0", "@stylistic/stylelint-config": "1.0.1", "@stylistic/stylelint-plugin": "2.1.0", "@tiptap/extension-character-count": "2.2.4", "@tiptap/extension-placeholder": "2.2.4", "@tiptap/extension-subscript": "2.2.4", "@tiptap/extension-superscript": "2.2.4", "@tiptap/extension-underline": "2.2.4", "@types/mapbox-gl": "2.7.21", "@types/node": "20.11.24", "@types/webfontloader": "1.6.38", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "@videojs-player/vue": "1.0.0", "@vitejs/plugin-vue": "5.0.4", "@vitejs/plugin-vue-jsx": "3.1.0", "eslint": "8.57.0", "eslint-config-airbnb-base": "15.0.0", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-case-police": "0.6.1", "eslint-plugin-import": "2.29.1", "eslint-plugin-promise": "6.1.1", "eslint-plugin-regex": "1.10.0", "eslint-plugin-sonarjs": "0.23.0", "eslint-plugin-unicorn": "50.0.1", "eslint-plugin-vue": "9.22.0", "msw": "2.2.2", "postcss-html": "1.6.0", "postcss-scss": "4.0.9", "sass": "1.71.1", "shikiji": "0.10.2", "stylelint": "16.2.1", "stylelint-config-idiomatic-order": "10.0.0", "stylelint-config-standard-scss": "13.0.0", "stylelint-use-logical-spec": "5.0.1", "tsx": "4.7.1", "type-fest": "4.10.3", "typescript": "5.3.3", "unplugin-auto-import": "0.17.5", "unplugin-vue-components": "0.26.0", "unplugin-vue-router": "0.7.0", "video.js": "8.6.0", "vite": "5.1.4", "vite-plugin-vue-devtools": "7.0.16", "vite-plugin-vue-layouts": "0.11.0", "vite-plugin-vuetify": "2.0.1", "vite-svg-loader": "5.1.0", "vue-shepherd": "3.0.0", "vue-tsc": "1.8.27"}, "resolutions": {"postcss": "^8", "@tiptap/core": "^2", "@types/video.js": "^7"}, "overrides": {"postcss": "^8", "@tiptap/core": "^2", "@types/video.js": "^7"}, "packageManager": "pnpm@8.6.2", "msw": {"workerDirectory": "public"}}