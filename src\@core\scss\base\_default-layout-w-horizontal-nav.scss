@use "@core/scss/base/placeholders" as *;
@use "@core/scss/template/placeholders" as *;
@use "@core/scss/base/mixins";

.layout-wrapper.layout-nav-type-horizontal {
  .layout-navbar-and-nav-container {
    @extend %default-layout-horizontal-nav-navbar-and-nav-container;
  }

  // 👉 Navbar
  .layout-navbar {
    @extend %default-layout-horizontal-nav-navbar;
  }

  // 👉 Layout content container
  .navbar-content-container {
    display: flex;
    align-items: center;
    block-size: 100%;
  }

  .layout-horizontal-nav {
    @extend %default-layout-horizontal-nav-nav;

    .nav-items {
      @extend %default-layout-horizontal-nav-nav-items-list;
    }
  }

  // 👉 App footer
  .layout-footer {
    @at-root {
      .layout-footer-sticky#{&} {
        background-color: rgb(var(--v-theme-surface));

        @include mixins.elevation(3);
      }
    }
  }

  // TODO: Use Vuetify grid sass variable here
  .layout-page-content {
    padding-block: 1.5rem;
  }
}
