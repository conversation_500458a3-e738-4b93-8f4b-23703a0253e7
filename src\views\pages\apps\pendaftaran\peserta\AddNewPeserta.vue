<script setup lang="ts">
import { VForm } from 'vuetify/components/VForm'
import { Indonesian } from 'flatpickr/dist/l10n/id'
import { usePendaftaran } from '@/stores/pendaftaran'
import avatar1 from '@images/avatars/default-avatar.png'
import { useNotyf } from '@/composables/useNotyf'

const props = withDefaults(defineProps<Props>(), {
  isFormDialogVisible: undefined,
  userData: () => ({
    nama: '',
    email: '',
    alamat: '',
    role: '',
    noHp: '',
    country: '',
    umur: '',
    jabatanId: '',
    avatar: '',
    tanggalLahir: '',
    jenis<PERSON><PERSON>min: '',
  }),
})

const emit = defineEmits<Emit>()

const getDataPeserta = usePendaftaran()

const notyf = useNotyf()
interface UserData {
  nama: string
  email: string
  alamat: string
  noHp: string
  role: string
  country: string
  umur: number
  jabatanId: string
  avatar: string
  tanggalLahir: string
  jenisKelamin: string
}

interface Jabatan {
  id: string
  nama_jabatan: string
}
interface Props {
  userData?: UserData
  isFormDialogVisible: boolean
  isMusda: boolean
  jabatanSelected?: Jabatan
}

interface Emit {
  (e: 'refetch'): void
  (e: 'update:isFormDialogVisible', val: boolean): void
}

const isFormValid = ref(false)
const isLoading = ref(false)
const refForm = ref<VForm>()
const fileKta = ref<File[] | any>([])
const fileFoto = ref<File[] | any>([])
const errors = ref()
const jabatans = ref([])
const userData = ref<UserData>(structuredClone(toRaw(props.userData)))
const configs = ref({ dateFormat: 'Y-m-d', altFormat: 'd-m-Y', altInput: true, locale: Indonesian })

// const refInputEl = ref<HTMLElement>()

const fileRules = [requiredValidator,
  (fileList: FileList) => !fileList || !fileList.length || fileList[0].size < 5000000 || 'File maksimal 5 MB!']

watch(props, () => {
  userData.value = structuredClone(toRaw(props.userData))
})

const onFormReset = () => {
  userData.value = structuredClone(toRaw(props.userData))

  emit('update:isFormDialogVisible', false)
}

const dialogModelValueUpdate = (val: boolean) => {
  emit('update:isFormDialogVisible', val)
}

const defaultFoto = computed(() => {
  if (!userData.value.avatar)
    return avatar1
  else
    return userData.value.avatar
})

const getJabatan = async () => {
  try {
    const res = await $api('/jabatan/get', {
      method: 'GET',
      onResponseError({ response }) {
        errors.value = response._data.errors
      },
    })

    jabatans.value = res.content

    await nextTick(() => {
      // router.replace(route.query.to ? String(route.query.to) : '/')
    })
  }
  catch (err) {
    console.error(err)
  }
}

const onFormSubmit = async () => {
  try {
    isLoading.value = true

    const form = new FormData()

    form.append('nama', userData.value.nama)
    form.append('email', userData.value.email)
    form.append('no_hp', userData.value.noHp)
    form.append('alamat', userData.value.alamat)
    form.append('tgl_lahir', userData.value.tanggalLahir)
    form.append('umur', ageCalculate.value)
    form.append('jabatan_id', props.jabatanSelected.id)
    form.append('jenis_kelamin', userData.value.jenisKelamin)
    form.append('lampiran_foto', fileFoto.value[0])
    form.append('lampiran_kta', fileKta.value[0])
    form.append('is_status', props.isMusda === true ? 'musda' : 'muspanitra')
    form.append('pendaftaran_id', props.isMusda === true ? getDataPeserta.pendaftaran[0].pendaftaran?.id : getDataPeserta.pendaftaran[1].pendaftaran?.id)

    const res = await $api('/peserta/create', {
      method: 'POST',
      body: form,
      onResponseError({ response }) {
        isLoading.value = false
        notyf.dismissAll()
        notyf.error(response._data.content[0].message!)
      },
    })

    isLoading.value = false
    notyf.dismissAll()
    notyf.success('Peserta berhasil ditambahkan')
    emit('update:isFormDialogVisible', false)

    emit('refetch')
    await getDataPeserta.getDataPeserta()
    await nextTick(() => {
      refForm.value?.reset()
      refForm.value?.resetValidation()
    })
  }
  catch (err) {
    isLoading.value = false
    console.error(err)
  }
}

const onSubmit = () => {
  refForm.value?.validate()
    .then(({ valid: isValid }) => {
      if (isValid)
        onFormSubmit()
    })
}

// changeAvatar function
const changeAvatar = (file: Event) => {
  const fileReader = new FileReader()
  const { files } = file.target as HTMLInputElement

  if (files && files.length) {
    fileReader.readAsDataURL(files[0])
    fileReader.onload = () => {
      if (typeof fileReader.result === 'string')
        userData.value.avatar = fileReader.result
    }
  }
}

const checkDigit = (event: KeyboardEvent) => {
  if (event.key.length === 1 && Number.isNaN(Number(event.key)))
    event.preventDefault()
}

const ageCalculate = computed(() => {
  if (userData.value.tanggalLahir) {
    const birthdateDate = new Date(userData.value.tanggalLahir)
    const now = new Date()
    const diff = now.getTime() - birthdateDate.getTime()

    return Math.floor(diff / (1000 * 60 * 60 * 24 * 365.25))
  }
  else {
    return 0
  }
})

watchEffect(() => {
  // eslint-disable-next-line sonarjs/no-collapsible-if
  if (props.isFormDialogVisible) {
    if (fileFoto.value)
      userData.value.avatar = ''
  }
  else {
    fileFoto.value = null
    fileKta.value = null
  }
})
</script>

<template>
  <VDialog :width="$vuetify.display.smAndDown ? 'auto' : 1000" :model-value="props.isFormDialogVisible" persistent
    no-click-animation :retain-focus="false" @update:model-value="dialogModelValueUpdate">
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="dialogModelValueUpdate(false)" />

    <VCard>
      <VCardText>
        <!-- 👉 Title -->
        <h4 class="text-h4 text-center">
          Formulir Peserta {{ isMusda ? 'Musda' : 'Muspanitra' }}
        </h4>
        <p class="text-body-1 text-center">
          {{ props.jabatanSelected.nama_jabatan }}
        </p>
        <VDivider class="mb-4" />
        <!-- 👉 Form -->
        <VForm ref="refForm" v-model="isFormValid" @submit.prevent="onSubmit">
          <VRow>
            <VCol cols="12" md="8">
              <VRow>
                <!-- 👉 Nama -->
                <VCol cols="12" md="6">
                  <AppTextField v-model="userData.nama" label="Nama" placeholder="John" :rules="[requiredValidator]" />
                </VCol>

                <!-- 👉 Email -->
                <VCol cols="12" md="6">
                  <AppTextField v-model="userData.email" label="Email" placeholder="<EMAIL>"
                    :rules="[requiredValidator, emailValidator]" />
                </VCol>

                <!-- 👉 No.HP -->
                <VCol cols="12" md="6">
                  <AppTextField v-model="userData.noHp" label="No.HP" placeholder="0852xxxxxxxx"
                    :rules="[requiredValidator]" @keydown="checkDigit" />
                </VCol>

                <!-- 👉 Alamat -->
                <VCol cols="12" md="6">
                  <AppTextField v-model="userData.alamat" label="Alamat" placeholder="Jl..."
                    :rules="[requiredValidator]" />
                </VCol>

                <!-- 👉 Tanggal lahir -->
                <VCol cols="12" md="6">
                  <AppDateTimePicker v-model="userData.tanggalLahir" label="Tanggal Lahir" placeholder=""
                    :rules="[requiredValidator]" :config="configs" />
                </VCol>

                <!-- 👉 Umur -->
                <VCol cols="12" md="6">
                  <AppTextField v-model="ageCalculate" label="Umur" placeholder="0" readonly />
                </VCol>

                <!-- 👉 Jenis Kelamin -->
                <VCol cols="12" md="6">
                  <AppSelect v-model="userData.jenisKelamin" label="Jenis Kelamin" placeholder="Laki-Laki"
                    :items="['Laki-Laki', 'Perempuan']" :rules="[requiredValidator]" />
                </VCol>

                <!-- 👉 Jabatan -->
                <VCol cols="12" md="6" class="mt-6">
                  <VFileInput v-model="fileKta" :rules="fileRules" placeholder="Upload your documents"
                    accept="image/png, image/jpeg, image/jpg" label="Upload KTA" prepend-icon="
                  tabler-paperclip">
                    <template #selection="{ fileNames }">
                      <template v-for="fileName in fileNames" :key="fileName">
                        <VChip label size="small" color="primary" class="me-2">
                          {{ fileName }}
                        </VChip>
                      </template>
                    </template>
                  </VFileInput>
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12" md="4" class="text-center">
              <p class="text-body-1 text-center">
                Foto
              </p>

              <!-- 👉 Avatar -->
              <VAvatar rounded size="100" class="d-flex align-center justify-center mx-auto mt-2 mb-5 border rounded"
                :image="defaultFoto" />
              <VAlert color="primary" variant="outlined" class="gap-2">
                <span class="text-body-2 mb-2 text-center">
                  Hanya JPG, JPEG. Maksimal size 5MB. Pastikan foto tidak terbalik.
                </span>
              </VAlert>
              <!-- 👉 Upload Photo -->
              <div class="d-flex flex-column justify-center gap-5 mt-2">
                <VFileInput v-model="fileFoto" :rules="fileRules" placeholder="Upload your documents"
                  accept="image/jpeg, image/jpg" label="Upload Foto" prepend-icon="tabler-camera"
                  @input="changeAvatar" />
              </div>
            </VCol>
          </VRow>
          <!-- 👉 Submit and Cancel -->
          <VCol cols="12" class="d-flex flex-wrap justify-center gap-4 mt-5">
            <VBtn type="submit" :loading="isLoading" prepend-icon="tabler-device-floppy">
              Submit
            </VBtn>

            <VBtn color="secondary" variant="tonal" prepend-icon="tabler-square-rounded-x" @click="onFormReset">
              Batal
            </VBtn>
          </VCol>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
