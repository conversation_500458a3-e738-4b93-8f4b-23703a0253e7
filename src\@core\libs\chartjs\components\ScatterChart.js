import { CategoryScale, Chart as ChartJS, Legend, LineElement, LinearScale, PointElement, Title, Tooltip } from 'chart.js';
import { defineComponent } from 'vue';
import { Scatter } from 'vue-chartjs';
ChartJS.register(Title, Tooltip, Legend, PointElement, LineElement, CategoryScale, LinearScale);
export default defineComponent({
    name: 'Scatter<PERSON><PERSON>',
    props: {
        chartId: {
            type: String,
            default: 'scatter-chart',
        },
        width: {
            type: Number,
            default: 400,
        },
        height: {
            type: Number,
            default: 400,
        },
        cssClasses: {
            default: '',
            type: String,
        },
        styles: {
            type: Object,
            default: () => ({}),
        },
        plugins: {
            type: Array,
            default: () => [],
        },
        chartData: {
            type: Object,
            default: () => ({}),
        },
        chartOptions: {
            type: Object,
            default: () => ({}),
        },
    },
    setup(props) {
        return () => h(h(Scatter), {
            data: props.chartData,
            options: props.chartOptions,
            chartId: props.chartId,
            width: props.width,
            height: props.height,
            cssClasses: props.cssClasses,
            styles: props.styles,
            plugins: props.plugins,
        });
    },
});
//# sourceMappingURL=ScatterChart.js.map