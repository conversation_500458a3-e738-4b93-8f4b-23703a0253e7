<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Aplikasi Pendaftaran Musda Aceh 2025</title>
  <meta name="description" content="Aplikasi untuk pendaftaran peserta musda & muspanitra aceh 2025">
  <meta name="author" content="Maopedia.com">
  <meta name="keywords" content="Musda Aceh, Pendaftaran, Aplikasi">
  <link rel="stylesheet" type="text/css" href="/loader.css" />
</head>

<body>
  <div id="app">
    <div id="loading-bg">
      <div class=" loading">
        <div class="effect-1 effects"></div>
        <div class="effect-2 effects"></div>
        <div class="effect-3 effects"></div>
      </div>
    </div>
  </div>
  <script type="module" src="/src/main.ts"></script>
  <script>const loaderColor = localStorage.getItem('vuexy-initial-loader-bg') || '#FFFFFF'
    const primaryColor = localStorage.getItem('vuexy-initial-loader-color') || '#7367F0'

    if (loaderColor)
      document.documentElement.style.setProperty('--initial-loader-bg', loaderColor)
    if (loaderColor)
      document.documentElement.style.setProperty('--initial-loader-bg', loaderColor)

    if (primaryColor)
      document.documentElement.style.setProperty('--initial-loader-color', primaryColor)
    </script>
  </body>
</html>
