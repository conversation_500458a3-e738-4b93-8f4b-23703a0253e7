import type { AllowedComponentProps, ComponentCustomProps, ComponentOptionsBase, ComponentOptionsMixin, CreateComponentPublicInstance, RendererElement, RendererNode, SlotsType, StyleValue, VNodeChild, VNodeProps } from 'vue'
import type { UserThemeConfig } from './types'
import type { LayoutConfig } from '@layouts/types'

export const defineThemeConfig = (userConfig: {
  app: {
    contentWidth: 'boxed'
    iconRenderer: {
      new(...args: any[]): CreateComponentPublicInstance<{ end: boolean; start: boolean; style: StyleValue; size: string | number; tag: string } & { color?: string | undefined; class?: any; icon?: IconValue | undefined; theme?: string | undefined } & {
        $children?: VNodeChild | (() => VNodeChild) | { default?: (() => VNodeChild) | undefined }
        'v-slots'?: { default?: false | (() => VNodeChild) | undefined } | undefined
      } & { 'v-slot:default'?: false | (() => VNodeChild) | undefined }, {}, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, Record<string, any>, VNodeProps & AllowedComponentProps & ComponentCustomProps & { end: boolean; start: boolean; style: StyleValue; size: string | number; tag: string } & {
        color?: string | undefined
        class?: any
        icon?: IconValue | undefined
        theme?: string | undefined
      } & { $children?: VNodeChild | (() => VNodeChild) | { default?: (() => VNodeChild) | undefined }; 'v-slots'?: { default?: false | (() => VNodeChild) | undefined } | undefined } & { 'v-slot:default'?: false | (() => VNodeChild) | undefined }, {
        end: boolean
        start: boolean
        style: StyleValue
        size: string | number
        tag: string
      }, true, {}, SlotsType<Partial<{ default: () => VNode<RendererNode, RendererElement, { [p: string]: any }>[] }>>, { P: {}; B: {}; D: {}; C: {}; M: {}; Defaults: {} }, { end: boolean; start: boolean; style: StyleValue; size: string | number; tag: string } & {
        color?: string | undefined
        class?: any
        icon?: IconValue | undefined
        theme?: string | undefined
      } & { $children?: VNodeChild | (() => VNodeChild) | { default?: (() => VNodeChild) | undefined }; 'v-slots'?: { default?: false | (() => VNodeChild) | undefined } | undefined } & { 'v-slot:default'?: false | (() => VNodeChild) | undefined }, {}, {}, {}, {}, {
        end: boolean
        start: boolean
        style: StyleValue
        size: string | number
        tag: string
      }>; __isFragment?: undefined; __isTeleport?: undefined; __isSuspense?: undefined
    } & ComponentOptionsBase<{ end: boolean; start: boolean; style: StyleValue; size: string | number; tag: string } & { color?: string | undefined; class?: any; icon?: IconValue | undefined; theme?: string | undefined } & {
      $children?: VNodeChild | (() => VNodeChild) | { default?: (() => VNodeChild) | undefined }
      'v-slots'?: { default?: false | (() => VNodeChild) | undefined } | undefined
    } & { 'v-slot:default'?: false | (() => VNodeChild) | undefined }, {}, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, Record<string, any>, string, { end: boolean; start: boolean; style: StyleValue; size: string | number; tag: string }, {}, string, SlotsType<Partial<{
      default: () => VNode<RendererNode, RendererElement, { [p: string]: any }>[]
    }>>> & VNodeProps & AllowedComponentProps & ComponentCustomProps & FilterPropsOptions<{
      theme: StringConstructor
      tag: Omit<{ type: StringConstructor; default: string }, 'type' | 'default'> & { type: PropType<string>; default: string }
      size: { type: (StringConstructor | NumberConstructor)[]; default: string }
      class: PropType<any>
      style: { type: PropType<StyleValue>; default: null }
      color: StringConstructor
      start: BooleanConstructor
      end: BooleanConstructor
      icon: PropType<IconValue>
    }, ExtractPropTypes<{
      theme: StringConstructor
      tag: Omit<{ type: StringConstructor; default: string }, 'type' | 'default'> & { type: PropType<string>; default: string }
      size: { type: (StringConstructor | NumberConstructor)[]; default: string }
      class: PropType<any>
      style: { type: PropType<StyleValue>; default: null }
      color: StringConstructor
      start: BooleanConstructor
      end: BooleanConstructor
      icon: PropType<IconValue>
    }>>
    skin: 'default'
    logo: VNode<RendererNode, RendererElement, { [p: string]: any }>
    theme: string
    title: string
    overlayNavFromBreakpoint: number
    i18n: { defaultLocale: string; langConfig: ({ i18nLang: string; label: string; isRTL: boolean } | { i18nLang: string; label: string; isRTL: boolean } | { i18nLang: string; label: string; isRTL: boolean })[]; enable: boolean }
    contentLayoutNav: string
  }
  verticalNav: { defaultNavItemIconProps: { icon: string }; isVerticalNavSemiDark: boolean; isVerticalNavCollapsed: boolean }
  navbar: { navbarBlur: boolean; type: 'sticky' }
  footer: { type: 'static' }
  icons: { sectionTitlePlaceholder: { icon: string }; chevronRight: { size: number; icon: string }; verticalNavUnPinned: { icon: string }; chevronDown: { icon: string }; close: { icon: string }; verticalNavPinned: { icon: string } }
  horizontalNav: { type: string; popoverOffset: number; transition: string }
}): { themeConfig: UserThemeConfig; layoutConfig: LayoutConfig } => {
  return {
    themeConfig: userConfig,
    layoutConfig: {
      app: {
        title: userConfig.app.title,
        logo: userConfig.app.logo,
        contentWidth: userConfig.app.contentWidth,
        contentLayoutNav: userConfig.app.contentLayoutNav,
        overlayNavFromBreakpoint: userConfig.app.overlayNavFromBreakpoint,
        i18n: {
          enable: userConfig.app.i18n.enable,
        },
        iconRenderer: userConfig.app.iconRenderer,
      },
      navbar: {
        type: userConfig.navbar.type,
        navbarBlur: userConfig.navbar.navbarBlur,
      },
      footer: { type: userConfig.footer.type },
      verticalNav: {
        isVerticalNavCollapsed: userConfig.verticalNav.isVerticalNavCollapsed,
        defaultNavItemIconProps: userConfig.verticalNav.defaultNavItemIconProps,
      },
      horizontalNav: {
        type: userConfig.horizontalNav.type,
        transition: userConfig.horizontalNav.transition,
        popoverOffset: userConfig.horizontalNav.popoverOffset,
      },
      icons: {
        chevronDown: userConfig.icons.chevronDown,
        chevronRight: userConfig.icons.chevronRight,
        close: userConfig.icons.close,
        verticalNavPinned: userConfig.icons.verticalNavPinned,
        verticalNavUnPinned: userConfig.icons.verticalNavUnPinned,
        sectionTitlePlaceholder: userConfig.icons.sectionTitlePlaceholder,
      },
    },
  }
}
